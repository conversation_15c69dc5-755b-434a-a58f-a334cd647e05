/**
 * 图片管理路由
 * 处理图片上传、删除、查询等功能
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { query } = require('../config/database');
const { verifyToken } = require('./auth');

// 导入异步文件操作工具
const asyncFileUtils = require('../utils/asyncFileUtils');
const SubscriptionService = require('../utils/subscriptionService');

// 创建订阅服务实例
const subscriptionService = new SubscriptionService();

/**
 * 测试分页功能的端点（无需认证）
 * GET /api/images/test-pagination
 */
router.get('/test-pagination', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 10, 10);
        const offset = (page - 1) * limit;

        // 模拟数据
        const mockImages = [];
        for (let i = 1; i <= 25; i++) {
            mockImages.push({
                id: i,
                image_name: `test_image_${i}.jpg`,
                image_path: `/test/path/image_${i}.jpg`,
                file_size: Math.floor(Math.random() * 1000000),
                upload_date: new Date().toISOString()
            });
        }

        const total = mockImages.length;
        const totalPages = Math.ceil(total / limit);
        const paginatedImages = mockImages.slice(offset, offset + limit);

        res.json({
            success: true,
            message: '分页测试成功',
            data: paginatedImages,
            pagination: {
                page: page,
                limit: limit,
                total: total,
                totalPages: totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        });

        console.log(`🧪 分页测试: 第${page}页, ${paginatedImages.length}/${total} 张图片`);

    } catch (error) {
        console.error('❌ 分页测试失败:', error);
        res.status(500).json({
            success: false,
            message: '分页测试失败: ' + error.message
        });
    }
});

// 配置multer存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        // 从token中获取用户信息
        const userId = req.user.id;

        // 动态创建存储路径
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');

        // 获取订单号（从请求参数或表单数据中）
        const orderNumber = req.body.order_number || req.params.orderNumber || 'unknown';

        // 构建存储路径: C:\MLS\Order_Img\工厂名\年-月\日\订单号
        const uploadPath = path.join('C:', 'MLS', 'Order_Img', 'factory_temp', `${year}-${month}`, day, orderNumber);

        // 确保目录存在
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }

        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        // 生成唯一文件名
        const timestamp = Date.now();
        const randomNum = Math.random().toString(36).substr(2, 9);
        const ext = path.extname(file.originalname);
        const filename = `${timestamp}_${randomNum}${ext}`;
        cb(null, filename);
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('不支持的文件类型'), false);
    }
};

// 创建multer实例
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
        files: 1
    }
});

/**
 * 上传图片
 * POST /api/images/upload
 */
router.post('/upload', verifyToken, upload.single('file'), async (req, res) => {
    const startTime = Date.now();

    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '请选择要上传的文件'
            });
        }

        const { order_number, factory_name, shipping_number } = req.body;
        const userId = req.user.id;

        if (!order_number) {
            return res.status(400).json({
                success: false,
                message: '订单号不能为空'
            });
        }

        // 获取用户的工厂名称（从数据库验证）- 使用缓存优化
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const userFactoryName = users[0].factory_name;

        // 构建最终存储路径
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');

        const finalDir = path.join('C:', 'MLS', 'Order_Img', userFactoryName, `${year}-${month}`, day, order_number);

        // 确保最终目录存在（异步）
        await asyncFileUtils.ensureDirectory(finalDir);

        // 先移动文件到最终位置（使用临时文件名）
        const tempFinalPath = path.join(finalDir, req.file.filename);
        await asyncFileUtils.safeRenameFile(req.file.path, tempFinalPath);

        // 保存图片信息到数据库（先获取ID）
        const insertSql = `
            INSERT INTO Img_Info
            (factory_name, order_number, image_name, image_path, file_size, shipping_number, upload_date, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;

        const result = await query(insertSql, [
            userFactoryName,
            order_number,
            req.file.originalname,
            '', // 临时为空，稍后更新
            req.file.size,
            shipping_number || 'DEFAULT'
        ]);

        // 获取插入的ID
        const imageId = result.insertId;

        // 构建新的文件名：工厂名+订单号+年月日+id
        const dateStr = `${year}${month}${day}`;
        const fileExt = path.extname(req.file.originalname);
        const newFileName = `${userFactoryName}_${order_number}_${dateStr}_${imageId}${fileExt}`;

        // 构建新的完整路径
        const newFinalPath = path.join(finalDir, newFileName);

        // 重命名文件（异步）
        await asyncFileUtils.safeRenameFile(tempFinalPath, newFinalPath);

        // 更新数据库中的image_path
        const updateSql = `
            UPDATE Img_Info
            SET image_path = ?, image_name = ?
            WHERE id = ?
        `;

        await query(updateSql, [newFinalPath, newFileName, imageId]);

        // 异步发送订阅消息通知（不阻塞响应）
        setImmediate(async () => {
            try {
                const username = req.user.username;
                await subscriptionService.sendOrderImageUploadNotification(order_number, username);
            } catch (notificationError) {
                console.error('❌ 发送订阅消息通知失败:', notificationError.message);
            }
        });

        const processingTime = Date.now() - startTime;

        res.json({
            success: true,
            message: '图片上传成功',
            data: {
                id: imageId,
                image_name: newFileName,
                image_path: newFinalPath,
                file_size: req.file.size,
                order_number: order_number,
                factory_name: userFactoryName,
                processing_time: processingTime
            }
        });

        console.log(`📷 图片上传成功: ${req.file.originalname} -> ${newFileName} (${userFactoryName}) [${processingTime}ms]`);

    } catch (error) {
        const processingTime = Date.now() - startTime;
        console.error(`❌ 图片上传失败 [${processingTime}ms]:`, error);

        // 清理临时文件
        if (req.file && fs.existsSync(req.file.path)) {
            try {
                fs.unlinkSync(req.file.path);
            } catch (cleanupError) {
                console.error('清理临时文件失败:', cleanupError);
            }
        }

        res.status(500).json({
            success: false,
            message: '图片上传失败: ' + error.message,
            processing_time: processingTime
        });
    }
});

/**
 * 获取订单的图片列表（支持分页）
 * GET /api/images/order/:orderNumber?page=1&limit=10
 */
router.get('/order/:orderNumber', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const { shipping_number } = req.query;
        const userId = req.user.id;

        // 获取分页参数
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 10, 10); // 最大限制10张图片
        const offset = (page - 1) * limit;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 构建查询条件
        let whereCondition = 'WHERE factory_name = ? AND order_number = ?';
        let queryParams = [factoryName, orderNumber];

        if (shipping_number && shipping_number !== 'ALL') {
            whereCondition += ' AND shipping_number = ?';
            queryParams.push(shipping_number);
        }

        // 先查询总数
        const countSql = `
            SELECT COUNT(*) as total
            FROM Img_Info
            ${whereCondition}
        `;

        const countResult = await query(countSql, queryParams);
        const total = countResult[0].total;
        const totalPages = Math.ceil(total / limit);

        // 查询分页图片列表
        const imagesSql = `
            SELECT
                id,
                image_name,
                image_path,
                file_size,
                shipping_number,
                upload_date
            FROM Img_Info
            ${whereCondition}
            ORDER BY upload_date DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        const images = await query(imagesSql, queryParams);

        res.json({
            success: true,
            message: '获取图片列表成功',
            data: images,
            pagination: {
                page: page,
                limit: limit,
                total: total,
                totalPages: totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        });

        console.log(`🖼️ 获取图片列表: ${orderNumber}, 第${page}页, 共 ${images.length}/${total} 张图片`);

    } catch (error) {
        console.error('❌ 获取图片列表失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 删除图片
 * POST /api/images/delete
 */
router.post('/delete', verifyToken, async (req, res) => {
    try {
        const { imageIds } = req.body;
        const userId = req.user.id;
        
        if (!imageIds || !Array.isArray(imageIds) || imageIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请提供要删除的图片ID列表'
            });
        }
        
        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name 
            FROM Factory_Login 
            WHERE id = ? AND status = 1
        `;
        
        const users = await query(userSql, [userId]);
        
        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        
        const factoryName = users[0].factory_name;
        
        // 查询要删除的图片信息
        const placeholders = imageIds.map(() => '?').join(',');
        const selectSql = `
            SELECT id, image_path 
            FROM Img_Info 
            WHERE id IN (${placeholders}) AND factory_name = ?
        `;
        
        const images = await query(selectSql, [...imageIds, factoryName]);
        
        if (images.length === 0) {
            return res.status(404).json({
                success: false,
                message: '未找到要删除的图片'
            });
        }
        
        // 删除文件系统中的图片文件
        let deletedFiles = 0;
        for (const image of images) {
            let filePath;

            // 判断路径格式
            if (image.image_path.startsWith('C:')) {
                // 新的完整路径格式：C:\MLS\Order_Img\工厂名\年-月\日\订单号\文件名
                filePath = image.image_path;
            } else if (image.image_path.startsWith('/order-images/')) {
                // 旧的相对路径格式：/order-images/工厂名/年-月/订单号/文件名
                const relativePath = image.image_path.replace('/order-images/', '');
                filePath = path.join('C:', 'MLS', 'Order_Img', relativePath);
            } else if (image.image_path.startsWith('/uploads/')) {
                // 更旧的路径格式：/uploads/工厂名/年-月/订单号/文件名
                const relativePath = image.image_path.replace('/uploads/', '');
                filePath = path.join('C:', 'MLS', 'Order_Img', relativePath);
            } else {
                // 其他格式，尝试作为相对路径处理
                filePath = path.join('C:', 'MLS', 'Order_Img', image.image_path);
            }

            if (fs.existsSync(filePath)) {
                try {
                    fs.unlinkSync(filePath);
                    deletedFiles++;
                    console.log(`✅ 删除文件成功: ${filePath}`);
                } catch (fileError) {
                    console.error(`❌ 删除文件失败: ${filePath}`, fileError);
                }
            } else {
                console.warn(`⚠️ 文件不存在: ${filePath}`);
            }
        }
        
        // 从数据库中删除记录
        const deleteSql = `
            DELETE FROM Img_Info 
            WHERE id IN (${placeholders}) AND factory_name = ?
        `;
        
        const result = await query(deleteSql, [...imageIds, factoryName]);
        
        res.json({
            success: true,
            message: `成功删除 ${result.affectedRows} 条记录`,
            data: {
                deleted_records: result.affectedRows,
                deleted_files: deletedFiles
            }
        });
        
        console.log(`🗑️ 删除图片: ${result.affectedRows} 条记录, ${deletedFiles} 个文件`);

    } catch (error) {
        console.error('❌ 删除图片失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取图片文件（公开访问，但验证图片所有权）
 * GET /api/images/file/:imageId
 */
router.get('/file/:imageId', async (req, res) => {
    try {
        const { imageId } = req.params;

        console.log(`🖼️ 请求图片文件: imageId=${imageId}`);

        // 直接查询图片信息（不需要用户认证，但会验证图片是否存在）

        // 直接查询图片信息
        const imageSql = `
            SELECT image_path, image_name, factory_name
            FROM Img_Info
            WHERE id = ?
        `;

        const images = await query(imageSql, [imageId]);

        if (images.length === 0) {
            console.error(`❌ 图片不存在: imageId=${imageId}`);
            return res.status(404).json({
                success: false,
                message: '图片不存在'
            });
        }

        const image = images[0];
        console.log(`📁 图片信息:`, {
            image_path: image.image_path,
            image_name: image.image_name,
            factory_name: image.factory_name
        });
        let filePath;

        // 判断路径格式并构建完整路径
        if (image.image_path.startsWith('C:')) {
            // 新的完整路径格式
            filePath = image.image_path;
        } else if (image.image_path.startsWith('/order-images/')) {
            // 旧的相对路径格式
            const relativePath = image.image_path.replace('/order-images/', '');
            filePath = path.join('C:', 'MLS', 'Order_Img', relativePath);
        } else if (image.image_path.startsWith('/uploads/')) {
            // 更旧的路径格式
            const relativePath = image.image_path.replace('/uploads/', '');
            filePath = path.join('C:', 'MLS', 'Order_Img', relativePath);
        } else {
            // 其他格式
            filePath = path.join('C:', 'MLS', 'Order_Img', image.image_path);
        }

        console.log(`📂 构建的文件路径: ${filePath}`);

        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            console.error(`❌ 图片文件不存在: ${filePath}`);
            return res.status(404).json({
                success: false,
                message: '图片文件不存在'
            });
        }

        console.log(`✅ 图片文件存在，准备发送: ${filePath}`);

        // 设置响应头
        const ext = path.extname(filePath).toLowerCase();
        const mimeTypes = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.webp': 'image/webp'
        };

        const contentType = mimeTypes[ext] || 'application/octet-stream';
        res.setHeader('Content-Type', contentType);

        // 设置缓存头以提高性能
        res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天

        // 发送文件
        res.sendFile(filePath);

    } catch (error) {
        console.error('❌ 获取图片文件失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;
