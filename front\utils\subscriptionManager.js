/**
 * 订阅消息管理器
 * 处理微信小程序订阅消息相关功能
 */

import apiService from './apiService.js';

class SubscriptionManager {
    constructor() {
        this.templateId = 'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8'; // 订单进度提醒模板
        this.openid = null;
        this.subscribed = false;
    }

    /**
     * 获取用户openid
     */
    async getOpenid() {
        try {
            // 如果已有openid，直接返回
            if (this.openid) {
                return this.openid;
            }

            // 从缓存中获取
            const cachedOpenid = uni.getStorageSync('user_openid');
            if (cachedOpenid) {
                this.openid = cachedOpenid;
                return this.openid;
            }

            // 通过微信登录获取code
            const loginResult = await uni.login();
            if (!loginResult.code) {
                throw new Error('获取微信登录code失败');
            }

            // 发送code到后端换取openid
            const response = await apiService.post('/subscription/get-openid', {
                code: loginResult.code
            });

            if (response.success && response.data.openid) {
                this.openid = response.data.openid;
                // 缓存openid
                uni.setStorageSync('user_openid', this.openid);
                console.log('✅ 获取openid成功');
                return this.openid;
            } else {
                throw new Error('获取openid失败');
            }
        } catch (error) {
            console.error('❌ 获取openid失败:', error);
            throw error;
        }
    }

    /**
     * 申请订阅消息权限
     */
    async requestSubscribeMessage() {
        try {
            console.log('🔔 申请订阅消息权限...');

            // 检查是否在微信小程序环境
            // #ifdef MP-WEIXIN
            const result = await uni.requestSubscribeMessage({
                tmplIds: [this.templateId]
            });

            console.log('订阅结果:', result);

            // 获取openid
            const openid = await this.getOpenid();

            // 保存订阅状态到后端
            if (result[this.templateId] === 'accept') {
                await this.saveSubscriptionStatus(result, openid);
                this.subscribed = true;
                uni.showToast({
                    title: '订阅成功',
                    icon: 'success'
                });
                return true;
            } else {
                console.log('用户拒绝订阅或其他状态:', result[this.templateId]);
                uni.showToast({
                    title: '订阅被拒绝',
                    icon: 'none'
                });
                return false;
            }
            // #endif

            // #ifndef MP-WEIXIN
            console.log('⚠️ 非微信小程序环境，跳过订阅消息申请');
            return false;
            // #endif

        } catch (error) {
            console.error('❌ 申请订阅消息失败:', error);
            uni.showToast({
                title: '订阅申请失败',
                icon: 'none'
            });
            return false;
        }
    }

    /**
     * 保存订阅状态到后端
     */
    async saveSubscriptionStatus(subscribeResult, openid) {
        try {
            const response = await apiService.post('/subscription/save', {
                subscribeResult: subscribeResult,
                openid: openid
            });

            if (response.success) {
                console.log('✅ 订阅状态保存成功');
            } else {
                console.error('❌ 订阅状态保存失败:', response.message);
            }
        } catch (error) {
            console.error('❌ 保存订阅状态异常:', error);
        }
    }

    /**
     * 检查用户订阅状态
     */
    async checkSubscriptionStatus() {
        try {
            const response = await apiService.get('/subscription/status');
            
            if (response.success) {
                this.subscribed = response.data.subscribed;
                console.log('📋 用户订阅状态:', this.subscribed ? '已订阅' : '未订阅');
                return this.subscribed;
            } else {
                console.error('❌ 获取订阅状态失败:', response.message);
                return false;
            }
        } catch (error) {
            console.error('❌ 检查订阅状态异常:', error);
            return false;
        }
    }

    /**
     * 获取消息发送历史
     */
    async getMessageHistory(page = 1, limit = 20) {
        try {
            const response = await apiService.get(`/subscription/message-history?page=${page}&limit=${limit}`);
            
            if (response.success) {
                return response.data;
            } else {
                console.error('❌ 获取消息历史失败:', response.message);
                return null;
            }
        } catch (error) {
            console.error('❌ 获取消息历史异常:', error);
            return null;
        }
    }

    /**
     * 手动发送测试通知（仅用于测试）
     */
    async sendTestNotification(orderNo) {
        try {
            const response = await apiService.post('/subscription/send-notification', {
                orderNo: orderNo
            });

            if (response.success) {
                uni.showToast({
                    title: '测试通知发送成功',
                    icon: 'success'
                });
                return true;
            } else {
                uni.showToast({
                    title: response.message || '发送失败',
                    icon: 'none'
                });
                return false;
            }
        } catch (error) {
            console.error('❌ 发送测试通知失败:', error);
            uni.showToast({
                title: '发送失败',
                icon: 'none'
            });
            return false;
        }
    }

    /**
     * 在适当的时机提示用户订阅
     */
    async promptSubscribeIfNeeded() {
        try {
            // 检查当前订阅状态
            const isSubscribed = await this.checkSubscriptionStatus();
            
            if (!isSubscribed) {
                // 显示订阅提示
                uni.showModal({
                    title: '消息通知',
                    content: '开启消息通知，及时了解订单图片上传状态',
                    confirmText: '开启通知',
                    cancelText: '暂不开启',
                    success: async (res) => {
                        if (res.confirm) {
                            await this.requestSubscribeMessage();
                        }
                    }
                });
            }
        } catch (error) {
            console.error('❌ 提示订阅失败:', error);
        }
    }

    /**
     * 清除缓存的openid（用于调试）
     */
    clearOpenidCache() {
        this.openid = null;
        uni.removeStorageSync('user_openid');
        console.log('🗑️ 已清除openid缓存');
    }
}

// 创建单例实例
const subscriptionManager = new SubscriptionManager();

export default subscriptionManager;
