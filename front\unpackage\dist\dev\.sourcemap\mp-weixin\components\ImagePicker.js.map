{"version": 3, "file": "ImagePicker.js", "sources": ["components/ImagePicker.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovRGVza3RvcC9XYXJlaG91c2UvZnJvbnQvY29tcG9uZW50cy9JbWFnZVBpY2tlci52dWU"], "sourcesContent": ["<template>\n\t<view class=\"image-picker\">\n\t\t<!-- 多图片预览区域 -->\n\t\t<view class=\"images-preview\" v-if=\"selectedImages.length > 0\">\n\t\t\t<view class=\"images-grid\">\n\t\t\t\t<view class=\"image-item\" v-for=\"(image, index) in selectedImages\" :key=\"index\">\n\t\t\t\t\t<image :src=\"image\" mode=\"aspectFit\" class=\"preview-image\"></image>\n\t\t\t\t\t<view class=\"image-remove\" @click=\"removeImage(index)\">\n\t\t\t\t\t\t<text class=\"remove-icon\">×</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"image-index\">{{index + 1}}</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 添加更多图片按钮 -->\n\t\t\t\t<view class=\"add-more-btn\" v-if=\"selectedImages.length < 9\" @click=\"chooseImage\">\n\t\t\t\t\t<text class=\"add-icon\">+</text>\n\t\t\t\t\t<text class=\"add-text\">添加图片</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"images-info\">\n\t\t\t\t<text class=\"info-text\">已选择 {{selectedImages.length}} 张图片</text>\n\t\t\t\t<text class=\"limit-text\">最多可选择9张</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-state\" v-else>\n\t\t\t<text class=\"empty-icon\">📷</text>\n\t\t\t<text class=\"empty-text\">请选择或拍摄图片</text>\n\t\t\t<text class=\"limit-text\">支持多张图片，最多9张</text>\n\t\t</view>\n\n\t\t<!-- 操作按钮区域 -->\n\t\t<view class=\"action-buttons\">\n\t\t\t<button class=\"btn btn-camera\" @click=\"takePhoto\">\n\t\t\t\t<text class=\"btn-icon\">📷</text>\n\t\t\t\t<text class=\"btn-text\">拍照</text>\n\t\t\t</button>\n\t\t\t<button class=\"btn btn-album\" @click=\"chooseImage\">\n\t\t\t\t<text class=\"btn-icon\">🖼️</text>\n\t\t\t\t<text class=\"btn-text\">相册</text>\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { showError, showSuccess } from '@/utils/helpers.js';\n\n\texport default {\n\t\tname: 'ImagePicker',\n\t\tprops: {\n\t\t\t// 当前选中的图片数组 (Vue 3 v-model)\n\t\t\tmodelValue: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault: () => []\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tselectedImages: []\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tmodelValue: {\n\t\t\t\thandler(newVal) {\n\t\t\t\t\tthis.selectedImages = Array.isArray(newVal) ? [...newVal] : [];\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 拍照\n\t\t\t */\n\t\t\ttakePhoto() {\n\t\t\t\tconsole.log('📷 点击拍照按钮');\n\t\t\t\tconst remainingCount = 9 - this.selectedImages.length;\n\t\t\t\tif (remainingCount <= 0) {\n\t\t\t\t\tshowError('最多只能选择9张图片');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// H5环境下的特殊处理\n\t\t\t\t// #ifdef H5\n\t\t\t\tconsole.log('🌐 H5环境：调用相册选择');\n\t\t\t\tthis.chooseImage();\n\t\t\t\t// #endif\n\n\t\t\t\t// 非H5环境使用拍照功能\n\t\t\t\t// #ifndef H5\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 1, // 拍照只能一张\n\t\t\t\t\tsourceType: ['camera'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('📷 拍照成功:', res);\n\t\t\t\t\t\tthis.handleImageSelect(res);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('📷 拍照失败:', err);\n\t\t\t\t\t\tshowError('拍照失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 从相册选择图片\n\t\t\t */\n\t\t\tchooseImage() {\n\t\t\t\tconsole.log('🖼️ 点击相册按钮');\n\t\t\t\tconst remainingCount = 9 - this.selectedImages.length;\n\t\t\t\tif (remainingCount <= 0) {\n\t\t\t\t\tshowError('最多只能选择9张图片');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: remainingCount, // 根据剩余数量动态设置\n\t\t\t\t\tsourceType: ['album'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('🖼️ 相册选择成功:', res);\n\t\t\t\t\t\tthis.handleImageSelect(res);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('🖼️ 选择图片失败:', err);\n\t\t\t\t\t\tshowError('选择图片失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理图片选择结果\n\t\t\t */\n\t\t\thandleImageSelect(res) {\n\t\t\t\tconsole.log('🔄 处理图片选择结果:', res);\n\t\t\t\tif (res.tempFilePaths && res.tempFilePaths.length > 0) {\n\t\t\t\t\t// 添加新选择的图片到现有数组\n\t\t\t\t\tconst newImages = [...this.selectedImages, ...res.tempFilePaths];\n\n\t\t\t\t\t// 确保不超过9张\n\t\t\t\t\tif (newImages.length > 9) {\n\t\t\t\t\t\tshowError('最多只能选择9张图片');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.selectedImages = newImages;\n\t\t\t\t\tconsole.log('✅ 选择图片成功，当前共:', this.selectedImages.length, '张');\n\n\t\t\t\t\t// 触发父组件事件 (Vue 3 v-model)\n\t\t\t\t\tthis.$emit('update:modelValue', this.selectedImages);\n\t\t\t\t\tthis.$emit('change', this.selectedImages);\n\t\t\t\t\tconsole.log('📤 已发送事件到父组件');\n\n\t\t\t\t\tshowSuccess(`成功添加 ${res.tempFilePaths.length} 张图片`);\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('❌ 图片选择失败，没有获取到文件路径');\n\t\t\t\t\tshowError('图片选择失败');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 移除指定索引的图片\n\t\t\t */\n\t\t\tremoveImage(index) {\n\t\t\t\tconsole.log('🗑️ 移除图片，索引:', index);\n\t\t\t\tthis.selectedImages.splice(index, 1);\n\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('update:modelValue', this.selectedImages);\n\t\t\t\tthis.$emit('change', this.selectedImages);\n\n\t\t\t\tshowSuccess('图片已移除');\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.image-picker {\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t/* 多图片预览区域 */\n\t.images-preview {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 40rpx;\n\t\tbox-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.images-grid {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.image-item {\n\t\tposition: relative;\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tborder-radius: 15rpx;\n\t\toverflow: hidden;\n\t}\n\n\t.preview-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder-radius: 15rpx;\n\t}\n\n\t.image-remove {\n\t\tposition: absolute;\n\t\ttop: 10rpx;\n\t\tright: 10rpx;\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tbackground: rgba(255, 0, 0, 0.8);\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.remove-icon {\n\t\tcolor: white;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.image-index {\n\t\tposition: absolute;\n\t\tbottom: 10rpx;\n\t\tleft: 10rpx;\n\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\tcolor: white;\n\t\tpadding: 5rpx 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 20rpx;\n\t}\n\n\t.add-more-btn {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tborder: 2rpx dashed #cccccc;\n\t\tborder-radius: 15rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: #fafafa;\n\t}\n\n\t.add-icon {\n\t\tfont-size: 60rpx;\n\t\tcolor: #999999;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.add-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t}\n\n\t.images-info {\n\t\ttext-align: center;\n\t}\n\n\t.info-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.limit-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tdisplay: block;\n\t}\n\n\t.file-name {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tdisplay: block;\n\t}\n\n\t/* 空状态 */\n\t.empty-state {\n\t\tbackground: rgba(255, 255, 255, 0.9);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 80rpx 30rpx;\n\t\ttext-align: center;\n\t\tmargin-bottom: 40rpx;\n\t\tborder: 2rpx dashed #cccccc;\n\t}\n\n\t.empty-icon {\n\t\tfont-size: 120rpx;\n\t\tmargin-bottom: 30rpx;\n\t\topacity: 0.6;\n\t\tdisplay: block;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #999999;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t/* 操作按钮 */\n\t.action-buttons {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tgap: 30rpx;\n\t}\n\n\t.btn {\n\t\tflex: 1;\n\t\tbackground: rgba(255, 255, 255, 0.9);\n\t\tborder: none;\n\t\tborder-radius: 15rpx;\n\t\tpadding: 40rpx 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tbox-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.btn:active {\n\t\ttransform: translateY(2rpx);\n\t\tbox-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.2);\n\t}\n\n\t.btn-icon {\n\t\tfont-size: 60rpx;\n\t\tmargin-bottom: 15rpx;\n\t\tdisplay: block;\n\t}\n\n\t.btn-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: 500;\n\t}\n\n\t.btn-camera {\n\t\tbackground: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);\n\t}\n\n\t.btn-album {\n\t\tbackground: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\n\t}\n</style>\n", "import Component from 'D:/Desktop/Warehouse/front/components/ImagePicker.vue'\nwx.createComponent(Component)"], "names": ["uni", "showError", "showSuccess"], "mappings": ";;;AAgDC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEN,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IACjB;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,gBAAgB,CAAC;AAAA,IAClB;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,YAAY;AAAA,MACX,QAAQ,QAAQ;AACf,aAAK,iBAAiB,MAAM,QAAQ,MAAM,IAAI,CAAC,GAAG,MAAM,IAAI;MAC5D;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,YAAY;AACXA,oBAAAA,MAAY,MAAA,OAAA,oCAAA,WAAW;AACvB,YAAM,iBAAiB,IAAI,KAAK,eAAe;AAC/C,UAAI,kBAAkB,GAAG;AACxBC,sBAAS,UAAC,YAAY;AACtB;AAAA,MACD;AAUAD,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA;AAAA,QACP,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAQ;AACjBA,wBAAY,MAAA,MAAA,OAAA,oCAAA,YAAY,GAAG;AAC3B,eAAK,kBAAkB,GAAG;AAAA,QAC1B;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,oCAAA,YAAY,GAAG;AAC7BC,wBAAS,UAAC,MAAM;AAAA,QACjB;AAAA,MACD,CAAC;AAAA,IAED;AAAA;AAAA;AAAA;AAAA,IAKD,cAAc;AACbD,oBAAAA,MAAY,MAAA,OAAA,qCAAA,YAAY;AACxB,YAAM,iBAAiB,IAAI,KAAK,eAAe;AAC/C,UAAI,kBAAkB,GAAG;AACxBC,sBAAS,UAAC,YAAY;AACtB;AAAA,MACD;AAEAD,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AACjBA,gFAAY,eAAe,GAAG;AAC9B,eAAK,kBAAkB,GAAG;AAAA,QAC1B;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,kFAAc,eAAe,GAAG;AAChCC,wBAAS,UAAC,QAAQ;AAAA,QACnB;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,kBAAkB,KAAK;AACtBD,oBAAY,MAAA,MAAA,OAAA,qCAAA,gBAAgB,GAAG;AAC/B,UAAI,IAAI,iBAAiB,IAAI,cAAc,SAAS,GAAG;AAEtD,cAAM,YAAY,CAAC,GAAG,KAAK,gBAAgB,GAAG,IAAI,aAAa;AAG/D,YAAI,UAAU,SAAS,GAAG;AACzBC,wBAAS,UAAC,YAAY;AACtB;AAAA,QACD;AAEA,aAAK,iBAAiB;AACtBD,4BAAA,MAAA,OAAA,qCAAY,iBAAiB,KAAK,eAAe,QAAQ,GAAG;AAG5D,aAAK,MAAM,qBAAqB,KAAK,cAAc;AACnD,aAAK,MAAM,UAAU,KAAK,cAAc;AACxCA,sBAAAA,wDAAY,cAAc;AAE1BE,sBAAW,YAAC,QAAQ,IAAI,cAAc,MAAM,MAAM;AAAA,aAC5C;AACNF,sBAAAA,MAAA,MAAA,OAAA,qCAAY,oBAAoB;AAChCC,sBAAS,UAAC,QAAQ;AAAA,MACnB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,OAAO;AAClBD,oBAAY,MAAA,MAAA,OAAA,qCAAA,gBAAgB,KAAK;AACjC,WAAK,eAAe,OAAO,OAAO,CAAC;AAGnC,WAAK,MAAM,qBAAqB,KAAK,cAAc;AACnD,WAAK,MAAM,UAAU,KAAK,cAAc;AAExCE,oBAAW,YAAC,OAAO;AAAA,IACpB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;AC7KD,GAAG,gBAAgB,SAAS;"}