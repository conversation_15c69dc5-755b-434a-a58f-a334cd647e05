
.camera-container.data-v-7b8d50ad {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
}
.header.data-v-7b8d50ad {
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}
.order-info.data-v-7b8d50ad {
		display: flex;
		align-items: center;
}
.order-icon.data-v-7b8d50ad {
		font-size: 50rpx;
		margin-right: 20rpx;
}
.order-details.data-v-7b8d50ad {
		display: flex;
		flex-direction: column;
}
.order-label.data-v-7b8d50ad {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 8rpx;
}
.order-number.data-v-7b8d50ad {
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
}
.camera-section.data-v-7b8d50ad {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.section-title.data-v-7b8d50ad {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
}
.title-icon.data-v-7b8d50ad {
		font-size: 36rpx;
		margin-right: 15rpx;
		color: #667eea;
}
.title-text.data-v-7b8d50ad {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
}
.camera-options.data-v-7b8d50ad {
		display: flex;
		gap: 20rpx;
}
.option-card.data-v-7b8d50ad {
		flex: 1;
		background: #f8f9ff;
		border: 2rpx solid #e0e6ff;
		border-radius: 16rpx;
		padding: 40rpx 20rpx;
		text-align: center;
		transition: all 0.3s ease;
}
.option-card.data-v-7b8d50ad:active {
		transform: scale(0.98);
		background: #f0f4ff;
}
.option-icon.data-v-7b8d50ad {
		margin-bottom: 20rpx;
}
.icon.data-v-7b8d50ad {
		font-size: 60rpx;
}
.option-text.data-v-7b8d50ad {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
		display: block;
}
.option-desc.data-v-7b8d50ad {
		font-size: 24rpx;
		color: #666666;
		display: block;
}
.preview-section.data-v-7b8d50ad {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.image-preview-list.data-v-7b8d50ad {
		white-space: nowrap;
}
.preview-item.data-v-7b8d50ad {
		position: relative;
		display: inline-block;
		width: 150rpx;
		height: 150rpx;
		margin-right: 20rpx;
		border-radius: 12rpx;
		overflow: hidden;
}
.preview-image.data-v-7b8d50ad {
		width: 100%;
		height: 100%;
}
.remove-btn.data-v-7b8d50ad {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		width: 40rpx;
		height: 40rpx;
		background: #ff4444;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 8rpx rgba(255, 68, 68, 0.3);
}
.remove-icon.data-v-7b8d50ad {
		font-size: 24rpx;
		color: #ffffff;
		font-weight: bold;
}
.upload-section.data-v-7b8d50ad {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.upload-btn.data-v-7b8d50ad {
		width: 100%;
		height: 90rpx;
		background: linear-gradient(135deg, #4CAF50, #45a049);
		border: none;
		border-radius: 12rpx;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: bold;
		transition: all 0.3s ease;
}
.upload-btn.data-v-7b8d50ad:not(:disabled):active {
		transform: scale(0.98);
}
.upload-btn.data-v-7b8d50ad:disabled {
		opacity: 0.6;
		transform: none;
}
.upload-btn.uploading.data-v-7b8d50ad {
		background: #cccccc;
}
.upload-modal.data-v-7b8d50ad {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
}
.upload-content.data-v-7b8d50ad {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		margin: 0 40rpx;
		min-width: 500rpx;
}
.upload-header.data-v-7b8d50ad {
		text-align: center;
		margin-bottom: 40rpx;
}
.upload-title.data-v-7b8d50ad {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
}
.progress-section.data-v-7b8d50ad {
		margin-bottom: 30rpx;
}
.progress-bar.data-v-7b8d50ad {
		width: 100%;
		height: 12rpx;
		background: #f0f0f0;
		border-radius: 6rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
}
.progress-fill.data-v-7b8d50ad {
		height: 100%;
		background: linear-gradient(135deg, #4CAF50, #45a049);
		transition: width 0.3s ease;
}
.progress-text.data-v-7b8d50ad {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		display: block;
}
.upload-status.data-v-7b8d50ad {
		text-align: center;
}
.status-text.data-v-7b8d50ad {
		font-size: 26rpx;
		color: #999999;
}
