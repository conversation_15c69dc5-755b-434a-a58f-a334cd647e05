"use strict";
const common_vendor = require("../common/vendor.js");
const getBaseUrl = () => {
  return "https://www.mls2005.top";
};
const API_CONFIG = {
  baseUrl: getBaseUrl(),
  timeout: 3e4
};
class ApiService {
  constructor() {
    this.baseUrl = API_CONFIG.baseUrl;
    this.timeout = API_CONFIG.timeout;
  }
  /**
   * 检查服务器连接
   */
  async checkServerConnection() {
    const maxRetries = 3;
    const retryDelay = 2e3;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        common_vendor.index.__f__("log", "at utils/apiService.js:48", `检查服务器连接 (尝试 ${attempt}/${maxRetries}):`, `${this.baseUrl}/api/health`);
        const response = await common_vendor.index.request({
          url: `${this.baseUrl}/api/health`,
          method: "GET",
          timeout: 15e3
          // 增加到15秒
        });
        common_vendor.index.__f__("log", "at utils/apiService.js:54", "服务器连接检查结果:", response);
        if (response.statusCode === 200) {
          common_vendor.index.__f__("log", "at utils/apiService.js:56", "✅ 服务器连接正常");
          return true;
        } else {
          common_vendor.index.__f__("log", "at utils/apiService.js:59", "❌ 服务器连接失败，状态码:", response.statusCode);
          if (attempt === maxRetries)
            return false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/apiService.js:63", `❌ 服务器连接检查失败 (尝试 ${attempt}/${maxRetries}):`, error);
        if (error.errno) {
          common_vendor.index.__f__("error", "at utils/apiService.js:67", "错误代码:", error.errno);
          if (error.errno === 600001) {
            common_vendor.index.__f__("error", "at utils/apiService.js:69", "网络连接被拒绝 - 可能原因:");
            common_vendor.index.__f__("error", "at utils/apiService.js:70", "1. 服务器未启动");
            common_vendor.index.__f__("error", "at utils/apiService.js:71", "2. 端口被防火墙阻止");
            common_vendor.index.__f__("error", "at utils/apiService.js:72", "3. 服务器地址或端口错误");
          } else if (error.errno === 5) {
            common_vendor.index.__f__("error", "at utils/apiService.js:74", "请求超时 - 可能原因:");
            common_vendor.index.__f__("error", "at utils/apiService.js:75", "1. 网络连接缓慢");
            common_vendor.index.__f__("error", "at utils/apiService.js:76", "2. 服务器响应缓慢");
            common_vendor.index.__f__("error", "at utils/apiService.js:77", "3. 防火墙阻止连接");
          }
        }
        if (attempt === maxRetries)
          return false;
        if (attempt < maxRetries) {
          common_vendor.index.__f__("log", "at utils/apiService.js:85", `等待 ${retryDelay / 1e3} 秒后重试...`);
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
        }
      }
    }
    return false;
  }
  /**
   * 通用图片识别
   * @param {string} imagePath 图片路径
   * @param {string} username 用户名（可选）
   */
  async recognizeImage(imagePath, username = null) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error("请求超时，请检查网络连接"));
      }, this.timeout);
      const formData = {};
      if (username) {
        formData.username = username;
      }
      common_vendor.index.uploadFile({
        url: `${this.baseUrl}/api/recognize`,
        filePath: imagePath,
        name: "image",
        formData,
        success: (uploadRes) => {
          clearTimeout(timeout);
          try {
            if (uploadRes.statusCode !== 200) {
              reject(new Error(`服务器错误 (${uploadRes.statusCode})`));
              return;
            }
            const result = JSON.parse(uploadRes.data);
            if (result.success) {
              resolve(result);
            } else {
              reject(new Error(result.message || "识别失败"));
            }
          } catch (parseError) {
            common_vendor.index.__f__("error", "at utils/apiService.js:130", "解析响应失败:", parseError, uploadRes.data);
            reject(new Error("服务器响应格式错误"));
          }
        },
        fail: (error) => {
          clearTimeout(timeout);
          common_vendor.index.__f__("error", "at utils/apiService.js:136", "上传失败:", error);
          let errorMessage = "上传失败";
          if (error.errMsg) {
            if (error.errMsg.includes("timeout")) {
              errorMessage = "请求超时，请检查网络连接";
            } else if (error.errMsg.includes("fail")) {
              errorMessage = "网络连接失败，请检查服务器是否启动";
            } else if (error.errMsg.includes("boundary")) {
              errorMessage = "文件上传格式错误，请重试";
            } else {
              errorMessage = error.errMsg;
            }
          }
          reject(new Error(errorMessage));
        }
      });
    });
  }
  /**
   * 批量图片识别（通用文字识别）
   * @param {Array} imagePaths 图片路径数组
   * @param {Function} progressCallback 进度回调函数
   */
  async recognizeMultipleImages(imagePaths, progressCallback) {
    const results = [];
    for (let i = 0; i < imagePaths.length; i++) {
      try {
        if (progressCallback) {
          progressCallback(i + 1, imagePaths.length);
        }
        const result = await this.recognizeImage(imagePaths[i]);
        results.push({
          index: i,
          imagePath: imagePaths[i],
          success: true,
          result
        });
      } catch (error) {
        results.push({
          index: i,
          imagePath: imagePaths[i],
          success: false,
          error: error.message
        });
      }
    }
    return results;
  }
  /**
   * 装柜放船样登记表识别
   * @param {string} imagePath 图片路径
   */
  async recognizeRegistrationForm(imagePath) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error("装柜放船样登记表识别超时"));
      }, this.timeout);
      common_vendor.index.uploadFile({
        url: `${this.baseUrl}/api/registration-form/recognize`,
        filePath: imagePath,
        name: "image",
        formData: {},
        success: (uploadRes) => {
          clearTimeout(timeout);
          try {
            if (uploadRes.statusCode !== 200) {
              resolve({ isRegistrationForm: false });
              return;
            }
            const result = JSON.parse(uploadRes.data);
            if (result.success && result.isRegistrationForm) {
              resolve(result);
            } else {
              resolve({ isRegistrationForm: false });
            }
          } catch (parseError) {
            common_vendor.index.__f__("error", "at utils/apiService.js:222", "解析装柜放船样登记表响应失败:", parseError);
            resolve({ isRegistrationForm: false });
          }
        },
        fail: (error) => {
          clearTimeout(timeout);
          common_vendor.index.__f__("log", "at utils/apiService.js:228", "装柜放船样登记表识别失败，使用普通识别:", error);
          resolve({ isRegistrationForm: false });
        }
      });
    });
  }
  /**
   * 装柜信息表识别
   * @param {string} imagePath 图片路径
   */
  async recognizeCartonInfo(imagePath) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error("装柜信息表识别超时"));
      }, this.timeout);
      common_vendor.index.uploadFile({
        url: `${this.baseUrl}/api/carton-info/recognize`,
        filePath: imagePath,
        name: "image",
        formData: {},
        success: (uploadRes) => {
          clearTimeout(timeout);
          try {
            if (uploadRes.statusCode !== 200) {
              resolve({ isCartonInfo: false });
              return;
            }
            const result = JSON.parse(uploadRes.data);
            if (result.success && result.isCartonInfo) {
              resolve(result);
            } else {
              resolve({ isCartonInfo: false });
            }
          } catch (parseError) {
            common_vendor.index.__f__("error", "at utils/apiService.js:265", "解析装柜信息表响应失败:", parseError);
            resolve({ isCartonInfo: false });
          }
        },
        fail: (error) => {
          clearTimeout(timeout);
          common_vendor.index.__f__("log", "at utils/apiService.js:271", "装柜信息表识别失败，使用普通识别:", error);
          resolve({ isCartonInfo: false });
        }
      });
    });
  }
  /**
   * 保存装柜放船样登记表数据
   * @param {Array} data 表格数据
   * @param {Object} imageInfo 图片信息
   */
  async saveRegistrationFormData(data, imageInfo) {
    const dataWithUploadTime = data.map((row) => ({
      ...row,
      UploadDate: (/* @__PURE__ */ new Date()).toISOString()
    }));
    const response = await common_vendor.index.request({
      url: `${this.baseUrl}/api/registration-form/save`,
      method: "POST",
      data: {
        data: dataWithUploadTime,
        imageInfo,
        uploadTime: (/* @__PURE__ */ new Date()).toISOString()
      },
      header: {
        "Content-Type": "application/json"
      }
    });
    if (response.statusCode === 200 && response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data.message || "保存失败");
    }
  }
  /**
   * 保存装柜信息表数据
   * @param {Object} data 装柜信息数据
   * @param {Object} imageInfo 图片信息
   */
  async saveCartonInfoData(data, imageInfo) {
    const dataWithUploadTime = {
      ...data,
      UploadDate: (/* @__PURE__ */ new Date()).toISOString()
    };
    const response = await common_vendor.index.request({
      url: `${this.baseUrl}/api/carton-info/save`,
      method: "POST",
      data: {
        data: dataWithUploadTime,
        imageInfo,
        uploadTime: (/* @__PURE__ */ new Date()).toISOString()
      },
      header: {
        "Content-Type": "application/json"
      }
    });
    if (response.statusCode === 200 && response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data.message || "保存失败");
    }
  }
  /**
   * 获取今日历史记录
   * @param {string} username - 用户名（可选）
   */
  async getTodayHistory(username = null) {
    try {
      common_vendor.index.__f__("log", "at utils/apiService.js:346", "📋 请求今日历史记录...");
      let url = `${this.baseUrl}/api/history/today`;
      if (username) {
        url += `?username=${encodeURIComponent(username)}`;
        common_vendor.index.__f__("log", "at utils/apiService.js:352", `📋 查询用户 ${username} 的历史记录`);
      }
      const response = await common_vendor.index.request({
        url,
        method: "GET",
        header: {
          "Content-Type": "application/json"
        },
        timeout: this.timeout
      });
      common_vendor.index.__f__("log", "at utils/apiService.js:364", "📋 历史记录响应:", response);
      if (response.statusCode === 200 && response.data.success) {
        return response.data;
      } else {
        throw new Error(response.data.message || "获取历史记录失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/apiService.js:372", "❌ 获取今日历史记录失败:", error);
      throw new Error("获取历史记录失败: " + error.message);
    }
  }
  /**
   * 通用GET请求方法
   * @param {string} endpoint - API端点（相对路径）
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async get(endpoint, options = {}) {
    try {
      const url = `${this.baseUrl}/api${endpoint}`;
      common_vendor.index.__f__("log", "at utils/apiService.js:386", `🔍 GET请求:`, url);
      const response = await common_vendor.index.request({
        url,
        method: "GET",
        header: {
          "Content-Type": "application/json",
          "Authorization": common_vendor.index.getStorageSync("token") ? `Bearer ${common_vendor.index.getStorageSync("token")}` : "",
          ...options.headers
        },
        timeout: options.timeout || this.timeout
      });
      common_vendor.index.__f__("log", "at utils/apiService.js:399", `✅ GET响应:`, response);
      if (response.statusCode === 200 && response.data.success) {
        return response.data;
      } else {
        throw new Error(response.data.message || `请求失败 (${response.statusCode})`);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/apiService.js:407", `❌ GET请求失败 (${endpoint}):`, error);
      throw new Error("请求失败: " + error.message);
    }
  }
  /**
   * 通用POST请求方法
   * @param {string} endpoint - API端点（相对路径）
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async post(endpoint, data = {}, options = {}) {
    try {
      const url = `${this.baseUrl}/api${endpoint}`;
      common_vendor.index.__f__("log", "at utils/apiService.js:422", `📤 POST请求:`, url, data);
      const response = await common_vendor.index.request({
        url,
        method: "POST",
        data,
        header: {
          "Content-Type": "application/json",
          "Authorization": common_vendor.index.getStorageSync("token") ? `Bearer ${common_vendor.index.getStorageSync("token")}` : "",
          ...options.headers
        },
        timeout: options.timeout || this.timeout
      });
      common_vendor.index.__f__("log", "at utils/apiService.js:436", `✅ POST响应:`, response);
      if (response.statusCode === 200 && response.data.success) {
        return response.data;
      } else {
        throw new Error(response.data.message || `请求失败 (${response.statusCode})`);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/apiService.js:444", `❌ POST请求失败 (${endpoint}):`, error);
      throw new Error("请求失败: " + error.message);
    }
  }
}
const apiService = new ApiService();
exports.apiService = apiService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/apiService.js.map
