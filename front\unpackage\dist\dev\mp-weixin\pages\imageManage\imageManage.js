"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_userManager = require("../../utils/userManager.js");
const utils_dataCache = require("../../utils/dataCache.js");
const utils_performanceMonitor = require("../../utils/performanceMonitor.js");
const utils_urlConfig = require("../../utils/urlConfig.js");
const _sfc_main = {
  data() {
    return {
      orderNumber: "",
      userInfo: {},
      imageList: [],
      selectedImages: [],
      isSelectMode: false,
      isLoading: false,
      isRefreshing: false,
      showPreview: false,
      currentPreviewIndex: 0,
      // 分页相关数据
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      // 活动的计时器列表
      activeTimers: /* @__PURE__ */ new Set(),
      // 发货单号相关
      shippingNumbers: [],
      currentShippingNumber: null,
      // 初始为null，等待发货单号加载完成后设置
      // 发货详细信息
      shippingDetails: [],
      // 新增信息相关
      showAddInfo: false,
      isSubmitting: false,
      addInfoForm: {
        length: "",
        weight_total: "",
        actual_price: ""
      }
    };
  },
  computed: {
    currentPreviewImage() {
      return this.imageList[this.currentPreviewIndex];
    },
    /**
     * 是否有图片数据
     */
    hasImages() {
      return this.imageList && Array.isArray(this.imageList) && this.imageList.length > 0;
    },
    /**
     * 是否应该显示空状态
     */
    shouldShowEmpty() {
      return !this.isLoading && !this.hasImages;
    }
  },
  methods: {
    /**
     * 获取发货单号列表
     */
    async loadShippingNumbers() {
      var _a;
      try {
        const requestConfig = utils_userManager.userManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl(`/api/orders/${encodeURIComponent(this.orderNumber)}/shipping-numbers`),
          method: "GET",
          timeout: 1e4
        });
        const response = await common_vendor.index.request(requestConfig);
        if (response.statusCode === 200 && response.data.success) {
          this.shippingNumbers = response.data.data;
          this.currentShippingNumber = "ALL";
          common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:431", "📦 获取发货单号列表成功:", this.shippingNumbers);
        } else {
          throw new Error(((_a = response.data) == null ? void 0 : _a.message) || "获取发货单号失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:436", "❌ 获取发货单号列表失败:", error);
        this.shippingNumbers = ["DEFAULT"];
        this.currentShippingNumber = "ALL";
      }
    },
    /**
     * 获取发货详细信息
     */
    async loadShippingDetails() {
      var _a;
      try {
        let queryParams = "";
        if (this.currentShippingNumber && this.currentShippingNumber !== "ALL") {
          queryParams = `?shipping_number=${encodeURIComponent(this.currentShippingNumber)}`;
        }
        const requestConfig = utils_userManager.userManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl(`/api/orders/${encodeURIComponent(this.orderNumber)}/shipping-details${queryParams}`),
          method: "GET",
          timeout: 1e4
        });
        const response = await common_vendor.index.request(requestConfig);
        if (response.statusCode === 200 && response.data.success) {
          this.shippingDetails = response.data.data;
          common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:464", "📋 获取发货详细信息成功:", this.shippingDetails);
        } else {
          common_vendor.index.__f__("warn", "at pages/imageManage/imageManage.vue:466", "获取发货详细信息失败:", (_a = response.data) == null ? void 0 : _a.message);
          this.shippingDetails = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:470", "❌ 获取发货详细信息失败:", error);
        this.shippingDetails = [];
      }
    },
    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString)
        return "-";
      try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:487", "日期格式化失败:", error);
        return "-";
      }
    },
    /**
     * 切换发货单号
     */
    async switchShippingNumber(shippingNumber) {
      if (this.currentShippingNumber === shippingNumber) {
        return;
      }
      this.currentShippingNumber = shippingNumber;
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:501", "🔄 切换发货单号:", shippingNumber);
      this.pagination.page = 1;
      await Promise.all([
        this.loadShippingDetails(),
        this.loadImages(true)
      ]);
    },
    /**
     * 跳转到拍照上传页面
     */
    goToCamera() {
      const currentShipping = this.currentShippingNumber === "ALL" ? this.shippingNumbers[0] || "DEFAULT" : this.currentShippingNumber;
      common_vendor.index.navigateTo({
        url: `/pages/camera/camera?orderNumber=${encodeURIComponent(this.orderNumber)}&shippingNumber=${encodeURIComponent(currentShipping)}`,
        success: () => {
          common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:522", "跳转到拍照页面");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:525", "跳转失败:", err);
          utils_helpers.showError("页面跳转失败");
        }
      });
    },
    /**
     * 分页相关方法
     */
    // 上一页
    goToPrevPage() {
      if (this.pagination.hasPrev) {
        this.pagination.page--;
        this.loadImages();
      }
    },
    // 下一页
    goToNextPage() {
      if (this.pagination.hasNext) {
        this.pagination.page++;
        this.loadImages();
      }
    },
    // 跳转到指定页
    goToPage(pageNum) {
      if (pageNum !== this.pagination.page && pageNum >= 1 && pageNum <= this.pagination.totalPages) {
        this.pagination.page = pageNum;
        this.loadImages();
      }
    },
    // 获取页码数组
    getPageNumbers() {
      const { page, totalPages } = this.pagination;
      const numbers = [];
      const start = Math.max(1, page - 2);
      const end = Math.min(totalPages, page + 2);
      for (let i = start; i <= end; i++) {
        numbers.push(i);
      }
      return numbers;
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "未知";
      try {
        const date = new Date(timeStr);
        return date.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit"
        });
      } catch (error) {
        return "未知";
      }
    },
    // 获取图片URL
    getImageUrl(imagePath, imageId) {
      return utils_urlConfig.urlConfig.getImageUrl(imagePath, imageId);
    },
    /**
     * 处理图片点击
     */
    handleImageClick(event) {
      const { image, index } = event;
      if (this.isSelectMode) {
        this.toggleImageSelection(image.id);
      } else {
        this.showImagePreview(index);
      }
    },
    /**
     * 处理图片长按
     */
    handleImageLongPress(image) {
      if (!this.isSelectMode) {
        this.isSelectMode = true;
        this.selectedImages = [image.id];
      }
    },
    /**
     * 切换选择模式
     */
    toggleSelectMode() {
      this.isSelectMode = !this.isSelectMode;
      if (!this.isSelectMode) {
        this.selectedImages = [];
      }
    },
    /**
     * 切换图片选择状态
     */
    toggleImageSelection(imageId) {
      const index = this.selectedImages.indexOf(imageId);
      if (index > -1) {
        this.selectedImages.splice(index, 1);
      } else {
        this.selectedImages.push(imageId);
      }
      if (this.selectedImages.length === 0) {
        this.isSelectMode = false;
      }
    },
    /**
     * 检查图片是否被选中
     */
    isImageSelected(imageId) {
      return this.selectedImages.includes(imageId);
    },
    /**
     * 显示图片预览
     */
    showImagePreview(index) {
      this.currentPreviewIndex = index;
      this.showPreview = true;
    },
    /**
     * 关闭图片预览
     */
    closePreview() {
      this.showPreview = false;
    },
    /**
     * 轮播图切换
     */
    onSwiperChange(e) {
      this.currentPreviewIndex = e.detail.current;
    },
    /**
     * 处理删除选中图片
     */
    async handleDeleteSelected() {
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:678", "🗑️ 开始删除操作，选中图片:", this.selectedImages);
      if (this.selectedImages.length === 0) {
        utils_helpers.showError("请先选择要删除的图片");
        return;
      }
      const confirmed = await utils_helpers.showConfirm(
        "确认删除",
        `确定要删除选中的 ${this.selectedImages.length} 张图片吗？此操作不可恢复。`
      );
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:690", "🗑️ 用户确认删除:", confirmed);
      if (confirmed) {
        await this.deleteImages();
      }
    },
    /**
     * 删除图片
     */
    async deleteImages() {
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:701", "🗑️ 执行删除请求，图片IDs:", this.selectedImages);
      try {
        const response = await this.deleteImagesRequest();
        common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:705", "🗑️ 删除响应:", response);
        if (response.success) {
          utils_helpers.showSuccess(`成功删除 ${this.selectedImages.length} 张图片`);
          this.selectedImages = [];
          this.isSelectMode = false;
          for (let page = 1; page <= this.pagination.totalPages; page++) {
            const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${page}`;
            utils_dataCache.dataCacheManager.removeCache("images", cacheKey);
          }
          this.pagination.page = 1;
          await this.loadImages(true);
        } else {
          common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:724", "🗑️ 删除失败:", response);
          utils_helpers.showError(response.message || "删除失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:728", "🗑️ 删除图片异常:", error);
        utils_helpers.showError("删除失败：" + error.message);
      }
    },
    /**
     * 删除图片请求
     */
    async deleteImagesRequest() {
      return new Promise((resolve, reject) => {
        const requestConfig = utils_userManager.userManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl("/api/images/delete"),
          method: "POST",
          data: {
            imageIds: this.selectedImages
          },
          timeout: 15e3,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`服务器错误 (${res.statusCode})`));
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:753", "删除图片请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
        common_vendor.index.request(requestConfig);
      });
    },
    /**
     * 加载图片列表（支持分页）
     */
    async loadImages(forceRefresh = false) {
      if (!this.orderNumber || !this.userInfo.factory_name || this.currentShippingNumber === null) {
        return;
      }
      const timerName = `loadImages_${this.orderNumber}_page${this.pagination.page}`;
      if (this.activeTimers.has(timerName)) {
        try {
          utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages/imageManage/imageManage.vue:778", "清理旧计时器失败:", error);
        }
        this.activeTimers.delete(timerName);
      }
      utils_performanceMonitor.performanceMonitor.startTimer(timerName);
      this.activeTimers.add(timerName);
      this.isLoading = true;
      try {
        const shippingKey = this.currentShippingNumber || "ALL";
        const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_${shippingKey}_page${this.pagination.page}`;
        const imageData = await utils_dataCache.dataCacheManager.getData(
          "images",
          cacheKey,
          () => this.getImagesRequest(),
          { forceRefresh, ttl: 300 }
          // 5分钟缓存
        );
        if (imageData.success) {
          this.imageList = imageData.data || [];
          if (imageData.pagination) {
            this.pagination = {
              ...this.pagination,
              ...imageData.pagination
            };
          }
          common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:811", `图片列表加载成功: 第${this.pagination.page}页, ${this.imageList.length}/${this.pagination.total} 张图片`);
          const loadDuration = utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
          this.activeTimers.delete(timerName);
          utils_performanceMonitor.performanceMonitor.recordLoadPerformance("imageList", this.imageList.length, loadDuration);
          utils_performanceMonitor.performanceMonitor.recordCacheHit("data", !forceRefresh);
        } else {
          common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:821", "获取图片列表失败:", imageData.message);
          this.imageList = [];
          this.pagination.total = 0;
          this.pagination.totalPages = 0;
          utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
          this.activeTimers.delete(timerName);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:829", "加载图片列表失败:", error);
        this.imageList = [];
        this.pagination.total = 0;
        this.pagination.totalPages = 0;
        utils_helpers.showError("加载图片列表失败");
        utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
        this.activeTimers.delete(timerName);
      } finally {
        this.isLoading = false;
      }
    },
    /**
     * 获取图片列表请求（支持分页）
     */
    async getImagesRequest() {
      return new Promise((resolve, reject) => {
        let queryParams = `page=${this.pagination.page}&limit=${this.pagination.limit}`;
        if (this.currentShippingNumber && this.currentShippingNumber !== "ALL") {
          queryParams += `&shipping_number=${encodeURIComponent(this.currentShippingNumber)}`;
        }
        const requestConfig = utils_userManager.userManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl(`/api/images/order/${encodeURIComponent(this.orderNumber)}?${queryParams}`),
          method: "GET",
          timeout: 1e4,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`服务器错误 (${res.statusCode})`));
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:864", "获取图片列表请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
        common_vendor.index.request(requestConfig);
      });
    },
    /**
     * 下拉刷新
     */
    async handleRefresh() {
      this.isRefreshing = true;
      try {
        await this.loadImages(true);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:882", "刷新失败:", error);
      } finally {
        this.isRefreshing = false;
      }
    },
    /**
     * 获取图片URL - 同步版本，用于模板渲染
     */
    getImageUrl(imagePath, imageId) {
      return utils_urlConfig.urlConfig.getImageUrl(imagePath, imageId);
    },
    /**
     * 处理图片加载错误
     */
    handleImageError(e) {
      var _a;
      common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:901", "图片加载失败:", e);
      common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:902", "失败的图片URL:", ((_a = e.target) == null ? void 0 : _a.src) || "未知");
      common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:903", "图片元素:", e.target);
    },
    /**
     * 格式化时间
     */
    formatTime(timeStr) {
      if (!timeStr)
        return "未知";
      try {
        const date = new Date(timeStr);
        return date.toLocaleString();
      } catch (error) {
        return "未知";
      }
    },
    /**
     * 显示性能报告（开发调试用）
     */
    showPerformanceReport() {
      const report = utils_performanceMonitor.performanceMonitor.generateReport();
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:925", report);
      {
        common_vendor.index.showModal({
          title: "性能报告",
          content: report,
          showCancel: false
        });
      }
    },
    /**
     * 处理图片上传事件
     */
    handleImageUploaded(data) {
      if (data.orderNumber === this.orderNumber) {
        common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:942", "收到图片上传事件，刷新图片列表");
        const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}`;
        utils_dataCache.dataCacheManager.removeCache("images", cacheKey);
        this.loadImages(true);
      }
    },
    /**
     * 清理所有活动的计时器
     */
    cleanupActiveTimers() {
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:954", "清理活动计时器，数量:", this.activeTimers.size);
      for (const timerName of this.activeTimers) {
        try {
          utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
          common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:958", "已清理计时器:", timerName);
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages/imageManage/imageManage.vue:960", "清理计时器失败:", timerName, error);
        }
      }
      this.activeTimers.clear();
    },
    /**
     * 显示新增信息弹窗
     */
    showAddInfoModal() {
      this.showAddInfo = true;
      this.addInfoForm = {
        length: "",
        weight_total: "",
        actual_price: ""
      };
    },
    /**
     * 关闭新增信息弹窗
     */
    closeAddInfoModal() {
      this.showAddInfo = false;
      this.isSubmitting = false;
    },
    /**
     * 提交新增信息
     */
    async submitAddInfo() {
      var _a;
      this.isSubmitting = true;
      try {
        const requestConfig = utils_userManager.userManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl("/api/orders/add-shipping-info"),
          method: "POST",
          data: {
            orderNumber: this.orderNumber,
            shippingNumber: this.currentShippingNumber === "ALL" ? this.shippingNumbers[0] || "DEFAULT" : this.currentShippingNumber,
            length: this.addInfoForm.length ? parseFloat(this.addInfoForm.length) : null,
            weight_total: this.addInfoForm.weight_total ? parseFloat(this.addInfoForm.weight_total) : null,
            actual_price: this.addInfoForm.actual_price ? parseFloat(this.addInfoForm.actual_price) : null
          },
          timeout: 1e4
        });
        const response = await common_vendor.index.request(requestConfig);
        if (response.statusCode === 200 && response.data.success) {
          common_vendor.index.showToast({
            title: "添加成功",
            icon: "success"
          });
          this.closeAddInfoModal();
          await this.loadShippingDetails();
        } else {
          throw new Error(((_a = response.data) == null ? void 0 : _a.message) || "添加失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:1025", "❌ 添加发货信息失败:", error);
        common_vendor.index.showToast({
          title: error.message || "添加失败",
          icon: "none"
        });
      } finally {
        this.isSubmitting = false;
      }
    },
    /**
     * 初始化
     */
    async init() {
      if (utils_userManager.userManager.requireLogin()) {
        this.userInfo = utils_userManager.userManager.getUserInfo();
        await Promise.all([
          this.loadShippingNumbers(),
          this.loadShippingDetails()
        ]);
        await this.loadImages();
      }
    }
  },
  onLoad(options) {
    this.orderNumber = options.orderNumber || "";
    if (!this.orderNumber) {
      utils_helpers.showError("订单号不能为空");
      common_vendor.index.navigateBack();
      return;
    }
    this.init();
    common_vendor.index.$on("imageUploaded", this.handleImageUploaded);
  },
  onShow() {
    if (this.orderNumber && this.userInfo.factory_name) {
      for (let page = 1; page <= 10; page++) {
        const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${page}`;
        utils_dataCache.dataCacheManager.removeCache("images", cacheKey);
      }
      this.pagination.page = 1;
      this.loadImages(true);
    }
  },
  onBackPress() {
    if (this.isSelectMode) {
      this.isSelectMode = false;
      this.selectedImages = [];
      return true;
    }
    this.cleanupActiveTimers();
    return false;
  },
  onUnload() {
    common_vendor.index.$off("imageUploaded", this.handleImageUploaded);
    this.cleanupActiveTimers();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: common_vendor.t($data.orderNumber),
    b: common_vendor.t($data.pagination.total || $data.imageList.length),
    c: $data.shippingNumbers.length === 1
  }, $data.shippingNumbers.length === 1 ? {
    d: common_vendor.t($data.shippingNumbers[0])
  } : {}, {
    e: $data.shippingDetails.length > 0
  }, $data.shippingDetails.length > 0 ? {
    f: common_vendor.o((...args) => $options.showAddInfoModal && $options.showAddInfoModal(...args)),
    g: common_vendor.f($data.shippingDetails, (detail, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.formatDate(detail.notice_date)),
        b: common_vendor.t(detail.shipping_no || "-"),
        c: common_vendor.t(detail.deliver_company || "-"),
        d: common_vendor.t(detail.follower || "-"),
        e: common_vendor.t(detail.product_name || "-"),
        f: common_vendor.t(detail.spec || "-"),
        g: common_vendor.t(detail.width || "-"),
        h: common_vendor.t(detail.weight || "-"),
        i: common_vendor.t(detail.quantity || "-"),
        j: detail.remark
      }, detail.remark ? {
        k: common_vendor.t(detail.remark)
      } : {}, {
        l: detail.length || detail.weight_total || detail.actual_price
      }, detail.length || detail.weight_total || detail.actual_price ? {
        m: common_vendor.t(detail.length || "-"),
        n: common_vendor.t(detail.weight_total || "-")
      } : {}, {
        o: detail.actual_price
      }, detail.actual_price ? {
        p: common_vendor.t(detail.actual_price || "-")
      } : {}, {
        q: index
      });
    })
  } : {}, {
    h: $data.shippingNumbers.length > 1
  }, $data.shippingNumbers.length > 1 ? {
    i: $data.currentShippingNumber === "ALL" ? 1 : "",
    j: common_vendor.o(($event) => $options.switchShippingNumber("ALL")),
    k: common_vendor.f($data.shippingNumbers, (shippingNo, index, i0) => {
      return {
        a: common_vendor.t(shippingNo),
        b: index,
        c: $data.currentShippingNumber === shippingNo ? 1 : "",
        d: common_vendor.o(($event) => $options.switchShippingNumber(shippingNo), index)
      };
    })
  } : {}, {
    l: common_vendor.o((...args) => $options.goToCamera && $options.goToCamera(...args)),
    m: common_vendor.t($data.isSelectMode ? "✓" : "🗑️"),
    n: common_vendor.t($data.isSelectMode ? "取消选择" : "选择删除"),
    o: $data.isSelectMode ? 1 : "",
    p: common_vendor.o((...args) => $options.toggleSelectMode && $options.toggleSelectMode(...args)),
    q: $data.isSelectMode
  }, $data.isSelectMode ? {
    r: common_vendor.t($data.selectedImages.length),
    s: $data.selectedImages.length === 0 ? 1 : "",
    t: $data.selectedImages.length === 0,
    v: common_vendor.o((...args) => $options.handleDeleteSelected && $options.handleDeleteSelected(...args))
  } : {}, {
    w: $data.isSelectMode
  }, $data.isSelectMode ? {} : {}, {
    x: $data.isLoading
  }, $data.isLoading ? {} : $options.hasImages ? {
    z: common_vendor.f($data.imageList, (image, index, i0) => {
      return common_vendor.e($data.isSelectMode ? common_vendor.e({
        a: $data.selectedImages.includes(image.id)
      }, $data.selectedImages.includes(image.id) ? {} : {}, {
        b: $data.selectedImages.includes(image.id) ? 1 : ""
      }) : {}, {
        c: $options.getImageUrl(image.image_path, image.id),
        d: common_vendor.o((...args) => $options.handleImageError && $options.handleImageError(...args), image.id),
        e: common_vendor.t(image.image_name),
        f: common_vendor.t($options.formatTime(image.upload_date)),
        g: image.id,
        h: common_vendor.o(($event) => $options.handleImageClick({
          image,
          index
        }), image.id),
        i: common_vendor.o(($event) => $options.handleImageLongPress(image), image.id)
      });
    }),
    A: $data.isSelectMode
  } : {}, {
    y: $options.hasImages,
    B: $data.pagination.totalPages > 1
  }, $data.pagination.totalPages > 1 ? {
    C: common_vendor.t($data.pagination.page),
    D: common_vendor.t($data.pagination.totalPages),
    E: common_vendor.t($data.pagination.total),
    F: !$data.pagination.hasPrev,
    G: common_vendor.o((...args) => $options.goToPrevPage && $options.goToPrevPage(...args)),
    H: common_vendor.f($options.getPageNumbers(), (pageNum, k0, i0) => {
      return {
        a: common_vendor.t(pageNum),
        b: $data.pagination.page === pageNum ? 1 : "",
        c: pageNum,
        d: common_vendor.o(($event) => $options.goToPage(pageNum), pageNum)
      };
    }),
    I: !$data.pagination.hasNext,
    J: common_vendor.o((...args) => $options.goToNextPage && $options.goToNextPage(...args))
  } : $options.shouldShowEmpty ? {} : {}, {
    K: $options.shouldShowEmpty,
    L: $data.showAddInfo
  }, $data.showAddInfo ? {
    M: common_vendor.o((...args) => $options.closeAddInfoModal && $options.closeAddInfoModal(...args)),
    N: $data.addInfoForm.length,
    O: common_vendor.o(($event) => $data.addInfoForm.length = $event.detail.value),
    P: $data.addInfoForm.weight_total,
    Q: common_vendor.o(($event) => $data.addInfoForm.weight_total = $event.detail.value),
    R: $data.addInfoForm.actual_price,
    S: common_vendor.o(($event) => $data.addInfoForm.actual_price = $event.detail.value),
    T: common_vendor.o((...args) => $options.closeAddInfoModal && $options.closeAddInfoModal(...args)),
    U: common_vendor.t($data.isSubmitting ? "提交中..." : "确认"),
    V: common_vendor.o((...args) => $options.submitAddInfo && $options.submitAddInfo(...args)),
    W: $data.isSubmitting,
    X: common_vendor.o(() => {
    }),
    Y: common_vendor.o((...args) => $options.closeAddInfoModal && $options.closeAddInfoModal(...args))
  } : {}, {
    Z: $data.showPreview
  }, $data.showPreview ? {
    aa: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args)),
    ab: common_vendor.f($data.imageList, (image, k0, i0) => {
      return {
        a: $options.getImageUrl(image.image_path, image.id),
        b: image.id
      };
    }),
    ac: $data.currentPreviewIndex,
    ad: common_vendor.o((...args) => $options.onSwiperChange && $options.onSwiperChange(...args)),
    ae: common_vendor.t((_a = $options.currentPreviewImage) == null ? void 0 : _a.image_name),
    af: common_vendor.t($data.currentPreviewIndex + 1),
    ag: common_vendor.t($data.imageList.length),
    ah: common_vendor.o(() => {
    }),
    ai: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-78a14e75"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/imageManage/imageManage.js.map
