
.container.data-v-9d986af9 {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
}
.header.data-v-9d986af9 {
		text-align: center;
		margin-bottom: 30rpx;
}
.title.data-v-9d986af9 {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
}
.status-card.data-v-9d986af9, .test-card.data-v-9d986af9, .history-card.data-v-9d986af9, .debug-card.data-v-9d986af9 {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.status-title.data-v-9d986af9, .test-title.data-v-9d986af9, .history-title.data-v-9d986af9, .debug-title.data-v-9d986af9 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
}
.status-content.data-v-9d986af9 {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.status-text.data-v-9d986af9 {
		font-size: 28rpx;
}
.subscribed.data-v-9d986af9 {
		color: #52c41a;
}
.not-subscribed.data-v-9d986af9 {
		color: #ff4d4f;
}
.subscribe-btn.data-v-9d986af9, .test-btn.data-v-9d986af9, .history-btn.data-v-9d986af9, .debug-btn.data-v-9d986af9 {
		background: #667eea;
		color: white;
		border: none;
		border-radius: 8rpx;
		padding: 16rpx 32rpx;
		font-size: 28rpx;
		margin: 10rpx;
}
.subscribe-btn.data-v-9d986af9:active, .test-btn.data-v-9d986af9:active, .history-btn.data-v-9d986af9:active, .debug-btn.data-v-9d986af9:active {
		background: #5a6fd8;
}
.input-group.data-v-9d986af9 {
		margin-bottom: 20rpx;
}
.input-label.data-v-9d986af9 {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 10rpx;
		display: block;
}
.input-field.data-v-9d986af9 {
		width: 100%;
		padding: 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 8rpx;
		font-size: 28rpx;
}
.history-list.data-v-9d986af9 {
		margin-top: 20rpx;
}
.history-item.data-v-9d986af9 {
		padding: 20rpx;
		border-bottom: 2rpx solid #f0f0f0;
}
.message-status.data-v-9d986af9 {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
}
.status-dot.data-v-9d986af9 {
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		margin-right: 16rpx;
}
.status-dot.success.data-v-9d986af9 {
		background: #52c41a;
}
.status-dot.failed.data-v-9d986af9 {
		background: #ff4d4f;
}
.status-label.data-v-9d986af9 {
		font-size: 26rpx;
		color: #333;
}
.message-time.data-v-9d986af9 {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 10rpx;
}
.error-msg.data-v-9d986af9 {
		font-size: 24rpx;
		color: #ff4d4f;
}
.no-history.data-v-9d986af9 {
		text-align: center;
		color: #999;
		padding: 40rpx;
}
.debug-content.data-v-9d986af9 {
		display: flex;
		flex-wrap: wrap;
}
