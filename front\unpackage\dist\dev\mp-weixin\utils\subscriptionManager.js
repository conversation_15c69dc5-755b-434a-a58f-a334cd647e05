"use strict";
const common_vendor = require("../common/vendor.js");
const utils_apiService = require("./apiService.js");
class SubscriptionManager {
  constructor() {
    this.templateId = "pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8";
    this.openid = null;
    this.subscribed = false;
  }
  /**
   * 获取用户openid
   */
  async getOpenid() {
    try {
      if (this.openid) {
        return this.openid;
      }
      const cachedOpenid = common_vendor.index.getStorageSync("user_openid");
      if (cachedOpenid) {
        this.openid = cachedOpenid;
        return this.openid;
      }
      const loginResult = await common_vendor.index.login();
      if (!loginResult.code) {
        throw new Error("获取微信登录code失败");
      }
      const response = await utils_apiService.apiService.post("/subscription/get-openid", {
        code: loginResult.code
      });
      if (response.success && response.data.openid) {
        this.openid = response.data.openid;
        common_vendor.index.setStorageSync("user_openid", this.openid);
        common_vendor.index.__f__("log", "at utils/subscriptionManager.js:47", "✅ 获取openid成功");
        return this.openid;
      } else {
        throw new Error("获取openid失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/subscriptionManager.js:53", "❌ 获取openid失败:", error);
      throw error;
    }
  }
  /**
   * 申请订阅消息权限
   */
  async requestSubscribeMessage() {
    try {
      common_vendor.index.__f__("log", "at utils/subscriptionManager.js:63", "🔔 申请订阅消息权限...");
      const result = await common_vendor.index.requestSubscribeMessage({
        tmplIds: [this.templateId]
      });
      common_vendor.index.__f__("log", "at utils/subscriptionManager.js:71", "订阅结果:", result);
      const openid = await this.getOpenid();
      if (result[this.templateId] === "accept") {
        await this.saveSubscriptionStatus(result, openid);
        this.subscribed = true;
        common_vendor.index.showToast({
          title: "订阅成功",
          icon: "success"
        });
        return true;
      } else {
        common_vendor.index.__f__("log", "at utils/subscriptionManager.js:86", "用户拒绝订阅或其他状态:", result[this.templateId]);
        common_vendor.index.showToast({
          title: "订阅被拒绝",
          icon: "none"
        });
        return false;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/subscriptionManager.js:101", "❌ 申请订阅消息失败:", error);
      common_vendor.index.showToast({
        title: "订阅申请失败",
        icon: "none"
      });
      return false;
    }
  }
  /**
   * 保存订阅状态到后端
   */
  async saveSubscriptionStatus(subscribeResult, openid) {
    try {
      const response = await utils_apiService.apiService.post("/subscription/save", {
        subscribeResult,
        openid
      });
      if (response.success) {
        common_vendor.index.__f__("log", "at utils/subscriptionManager.js:121", "✅ 订阅状态保存成功");
      } else {
        common_vendor.index.__f__("error", "at utils/subscriptionManager.js:123", "❌ 订阅状态保存失败:", response.message);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/subscriptionManager.js:126", "❌ 保存订阅状态异常:", error);
    }
  }
  /**
   * 检查用户订阅状态
   */
  async checkSubscriptionStatus() {
    try {
      const response = await utils_apiService.apiService.get("/subscription/status");
      if (response.success) {
        this.subscribed = response.data.subscribed;
        common_vendor.index.__f__("log", "at utils/subscriptionManager.js:139", "📋 用户订阅状态:", this.subscribed ? "已订阅" : "未订阅");
        return this.subscribed;
      } else {
        common_vendor.index.__f__("error", "at utils/subscriptionManager.js:142", "❌ 获取订阅状态失败:", response.message);
        return false;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/subscriptionManager.js:146", "❌ 检查订阅状态异常:", error);
      return false;
    }
  }
  /**
   * 获取消息发送历史
   */
  async getMessageHistory(page = 1, limit = 20) {
    try {
      const response = await utils_apiService.apiService.get(`/subscription/message-history?page=${page}&limit=${limit}`);
      if (response.success) {
        return response.data;
      } else {
        common_vendor.index.__f__("error", "at utils/subscriptionManager.js:161", "❌ 获取消息历史失败:", response.message);
        return null;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/subscriptionManager.js:165", "❌ 获取消息历史异常:", error);
      return null;
    }
  }
  /**
   * 手动发送测试通知（仅用于测试）
   */
  async sendTestNotification(orderNo) {
    try {
      const response = await utils_apiService.apiService.post("/subscription/send-notification", {
        orderNo
      });
      if (response.success) {
        common_vendor.index.showToast({
          title: "测试通知发送成功",
          icon: "success"
        });
        return true;
      } else {
        common_vendor.index.showToast({
          title: response.message || "发送失败",
          icon: "none"
        });
        return false;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/subscriptionManager.js:193", "❌ 发送测试通知失败:", error);
      common_vendor.index.showToast({
        title: "发送失败",
        icon: "none"
      });
      return false;
    }
  }
  /**
   * 在适当的时机提示用户订阅
   */
  async promptSubscribeIfNeeded() {
    try {
      const isSubscribed = await this.checkSubscriptionStatus();
      if (!isSubscribed) {
        common_vendor.index.showModal({
          title: "消息通知",
          content: "开启消息通知，及时了解订单图片上传状态",
          confirmText: "开启通知",
          cancelText: "暂不开启",
          success: async (res) => {
            if (res.confirm) {
              await this.requestSubscribeMessage();
            }
          }
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/subscriptionManager.js:225", "❌ 提示订阅失败:", error);
    }
  }
  /**
   * 清除缓存的openid（用于调试）
   */
  clearOpenidCache() {
    this.openid = null;
    common_vendor.index.removeStorageSync("user_openid");
    common_vendor.index.__f__("log", "at utils/subscriptionManager.js:235", "🗑️ 已清除openid缓存");
  }
}
const subscriptionManager = new SubscriptionManager();
exports.subscriptionManager = subscriptionManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/subscriptionManager.js.map
