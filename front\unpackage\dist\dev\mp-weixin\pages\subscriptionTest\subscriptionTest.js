"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_subscriptionManager = require("../../utils/subscriptionManager.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  data() {
    return {
      subscribed: false,
      testOrderNo: "",
      messageHistory: []
    };
  },
  onLoad() {
    this.checkSubscriptionStatus();
    this.loadMessageHistory();
  },
  methods: {
    /**
     * 申请订阅消息
     */
    async requestSubscribe() {
      try {
        common_vendor.index.showLoading({ title: "申请中..." });
        const success = await utils_subscriptionManager.subscriptionManager.requestSubscribeMessage();
        if (success) {
          this.subscribed = true;
          utils_helpers.showSuccess("订阅成功");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/subscriptionTest/subscriptionTest.vue:99", "申请订阅失败:", error);
        utils_helpers.showError("申请订阅失败");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 检查订阅状态
     */
    async checkSubscriptionStatus() {
      try {
        this.subscribed = await utils_subscriptionManager.subscriptionManager.checkSubscriptionStatus();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/subscriptionTest/subscriptionTest.vue:113", "检查订阅状态失败:", error);
      }
    },
    /**
     * 发送测试通知
     */
    async sendTestNotification() {
      if (!this.testOrderNo.trim()) {
        utils_helpers.showError("请输入订单号");
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "发送中..." });
        const success = await utils_subscriptionManager.subscriptionManager.sendTestNotification(this.testOrderNo.trim());
        if (success) {
          setTimeout(() => {
            this.loadMessageHistory();
          }, 1e3);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/subscriptionTest/subscriptionTest.vue:136", "发送测试通知失败:", error);
        utils_helpers.showError("发送失败");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 加载消息历史
     */
    async loadMessageHistory() {
      try {
        const result = await utils_subscriptionManager.subscriptionManager.getMessageHistory(1, 10);
        if (result) {
          this.messageHistory = result.messages || [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/subscriptionTest/subscriptionTest.vue:153", "加载消息历史失败:", error);
      }
    },
    /**
     * 清除OpenID缓存
     */
    clearOpenidCache() {
      utils_subscriptionManager.subscriptionManager.clearOpenidCache();
      utils_helpers.showSuccess("OpenID缓存已清除");
    },
    /**
     * 格式化时间
     */
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      const date = new Date(timeStr);
      return date.toLocaleString("zh-CN");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.subscribed ? "已订阅" : "未订阅"),
    b: common_vendor.n($data.subscribed ? "subscribed" : "not-subscribed"),
    c: !$data.subscribed
  }, !$data.subscribed ? {
    d: common_vendor.o((...args) => $options.requestSubscribe && $options.requestSubscribe(...args))
  } : {}, {
    e: $data.testOrderNo,
    f: common_vendor.o(($event) => $data.testOrderNo = $event.detail.value),
    g: common_vendor.o((...args) => $options.sendTestNotification && $options.sendTestNotification(...args)),
    h: !$data.testOrderNo,
    i: common_vendor.o((...args) => $options.loadMessageHistory && $options.loadMessageHistory(...args)),
    j: $data.messageHistory.length > 0
  }, $data.messageHistory.length > 0 ? {
    k: common_vendor.f($data.messageHistory, (message, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n(message.send_status === 1 ? "success" : "failed"),
        b: common_vendor.t(message.send_status === 1 ? "发送成功" : "发送失败"),
        c: common_vendor.t($options.formatTime(message.send_time)),
        d: message.error_msg
      }, message.error_msg ? {
        e: common_vendor.t(message.error_msg)
      } : {}, {
        f: index
      });
    })
  } : {}, {
    l: common_vendor.o((...args) => $options.clearOpenidCache && $options.clearOpenidCache(...args)),
    m: common_vendor.o((...args) => $options.checkSubscriptionStatus && $options.checkSubscriptionStatus(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9d986af9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/subscriptionTest/subscriptionTest.js.map
