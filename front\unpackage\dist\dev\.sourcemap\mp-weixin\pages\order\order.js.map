{"version": 3, "file": "order.js", "sources": ["pages/order/order.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvb3JkZXIvb3JkZXIudnVl"], "sourcesContent": ["<template>\n\t<view class=\"order-container\">\n\t\t<!-- 头部信息 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"factory-info\">\n\t\t\t\t<text class=\"factory-icon\">🏭</text>\n\t\t\t\t<view class=\"factory-details\">\n\t\t\t\t\t<text class=\"factory-name\">{{ userInfo.factory_name || '未知工厂' }}</text>\n\t\t\t\t\t<text class=\"welcome-text\">欢迎，{{ userInfo.username }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"logout-btn\" @click=\"handleLogout\">\n\t\t\t\t<text class=\"logout-icon\">🚪</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 订单查询区域 -->\n\t\t<view class=\"new-order-section\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-icon\">🔍</text>\n\t\t\t\t<text class=\"title-text\">订单查询</text>\n\t\t\t</view>\n\t\t\t<view class=\"input-group\">\n\t\t\t\t<input\n\t\t\t\t\tclass=\"order-input\"\n\t\t\t\t\ttype=\"text\"\n\t\t\t\t\tv-model=\"newOrderNumber\"\n\t\t\t\t\tplaceholder=\"请输入订单号进行查询\"\n\t\t\t\t\t:disabled=\"isLoading\"\n\t\t\t\t\t@confirm=\"handleQueryOrder\"\n\t\t\t\t/>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"query-btn\"\n\t\t\t\t\t:disabled=\"!newOrderNumber.trim() || isLoading\"\n\t\t\t\t\t@click=\"handleQueryOrder\"\n\t\t\t\t>\n\t\t\t\t\t<text class=\"btn-text\">查询</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 查询结果区域 -->\n\t\t<view class=\"query-result-section\" v-if=\"showQueryResult\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-icon\">🔍</text>\n\t\t\t\t<text class=\"title-text\">查询结果</text>\n\t\t\t\t<view class=\"close-query\" @click=\"closeQueryResult\">\n\t\t\t\t\t<text class=\"close-icon\">✕</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 查询加载状态 -->\n\t\t\t<view class=\"loading-section\" v-if=\"isQuerying\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<text class=\"loading-text\">查询中...</text>\n\t\t\t</view>\n\n\t\t\t<!-- 查询结果 -->\n\t\t\t<view class=\"query-content\" v-else>\n\t\t\t\t<!-- 查询成功 -->\n\t\t\t\t<view class=\"query-success\" v-if=\"queryResult\">\n\t\t\t\t\t<!-- 单个结果 -->\n\t\t\t\t\t<view v-if=\"!Array.isArray(queryResult)\" class=\"single-result\">\n\t\t\t\t\t\t<view class=\"order-item query-order-item\" @click=\"goToImageManage(queryResult.order_number)\">\n\t\t\t\t\t\t\t<view class=\"order-info\">\n\t\t\t\t\t\t\t\t<view class=\"order-number\">\n\t\t\t\t\t\t\t\t\t<text class=\"order-label\">订单号：</text>\n\t\t\t\t\t\t\t\t\t<text class=\"order-value\">{{ queryResult.order_number }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"order-time\">\n\t\t\t\t\t\t\t\t\t<text class=\"time-label\">创建时间：</text>\n\t\t\t\t\t\t\t\t\t<text class=\"time-value\">{{ formatTime(queryResult.created_date) }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"order-time\">\n\t\t\t\t\t\t\t\t\t<text class=\"time-label\">最新上传：</text>\n\t\t\t\t\t\t\t\t\t<text class=\"time-value\">{{ formatTime(queryResult.latest_upload) }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"order-images\">\n\t\t\t\t\t\t\t\t\t<text class=\"images-label\">图片数量：</text>\n\t\t\t\t\t\t\t\t\t<text class=\"images-value\">{{ queryResult.image_count || 0 }} 张</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"order-arrow\">\n\t\t\t\t\t\t\t\t<text class=\"arrow\">→</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 多个结果 -->\n\t\t\t\t\t<view v-else class=\"multiple-results\">\n\t\t\t\t\t\t<view class=\"result-header\">\n\t\t\t\t\t\t\t<text class=\"result-count\">找到 {{ queryResult.length }} 个匹配的订单</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<scroll-view class=\"query-list\" scroll-y=\"true\" :style=\"{ maxHeight: '400rpx' }\">\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"order-item query-order-item\"\n\t\t\t\t\t\t\t\tv-for=\"(order, index) in queryResult\"\n\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\t@click=\"goToImageManage(order.order_number)\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<view class=\"order-info\">\n\t\t\t\t\t\t\t\t\t<view class=\"order-number\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"order-label\">订单号：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"order-value\">{{ order.order_number }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"order-time\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"time-label\">创建时间：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"time-value\">{{ formatTime(order.created_date) }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"order-time\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"time-label\">最新上传：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"time-value\">{{ formatTime(order.latest_upload) }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"order-images\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"images-label\">图片数量：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"images-value\">{{ order.image_count || 0 }} 张</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"order-arrow\">\n\t\t\t\t\t\t\t\t\t<text class=\"arrow\">→</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 查询失败 -->\n\t\t\t\t<view class=\"query-empty\" v-else>\n\t\t\t\t\t<text class=\"empty-icon\">🔍</text>\n\t\t\t\t\t<text class=\"empty-text\">未找到匹配的订单</text>\n\t\t\t\t\t<text class=\"empty-hint\">请尝试输入订单号的部分内容进行模糊查询</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 历史订单列表 -->\n\t\t<view class=\"history-section\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-icon\">📋</text>\n\t\t\t\t<text class=\"title-text\">历史订单</text>\n\t\t\t\t<text class=\"order-count\">({{ orderList.length }})</text>\n\t\t\t</view>\n\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view class=\"loading-section\" v-if=\"isLoadingOrders\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<text class=\"loading-text\">加载订单列表中...</text>\n\t\t\t</view>\n\n\t\t\t<!-- 订单列表 -->\n\t\t\t<scroll-view\n\t\t\t\tclass=\"order-list\"\n\t\t\t\tscroll-y=\"true\"\n\t\t\t\tv-else-if=\"orderList.length > 0\"\n\t\t\t\trefresher-enabled=\"true\"\n\t\t\t\t:refresher-triggered=\"isRefreshing\"\n\t\t\t\t@refresherrefresh=\"handleRefresh\"\n\t\t\t\t:scroll-with-animation=\"true\"\n\t\t\t\t:enable-back-to-top=\"true\"\n\t\t\t>\n\t\t\t\t<view\n\t\t\t\t\tclass=\"order-item\"\n\t\t\t\t\tv-for=\"(order, index) in orderList\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"goToImageManage(order.order_number)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"order-info\">\n\t\t\t\t\t\t<view class=\"order-number\">\n\t\t\t\t\t\t\t<text class=\"order-label\">订单号：</text>\n\t\t\t\t\t\t\t<text class=\"order-value\">{{ order.order_number }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"order-time\">\n\t\t\t\t\t\t\t<text class=\"time-label\">创建时间：</text>\n\t\t\t\t\t\t\t<text class=\"time-value\">{{ formatTime(order.created_date) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"order-time\">\n\t\t\t\t\t\t\t<text class=\"time-label\">最新上传：</text>\n\t\t\t\t\t\t\t<text class=\"time-value\">{{ formatTime(order.latest_upload) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"order-images\">\n\t\t\t\t\t\t\t<text class=\"images-label\">图片数量：</text>\n\t\t\t\t\t\t\t<text class=\"images-value\">{{ order.image_count || 0 }} 张</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"order-arrow\">\n\t\t\t\t\t\t<text class=\"arrow\">→</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\n\t\t\t<!-- 空状态 -->\n\t\t\t<view class=\"empty-state\" v-else>\n\t\t\t\t<text class=\"empty-icon\">📦</text>\n\t\t\t\t<text class=\"empty-text\">暂无订单记录</text>\n\t\t\t\t<text class=\"empty-hint\">请先新增订单</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { showError } from '../../utils/helpers.js';\n\timport userManager from '../../utils/userManager.js';\n\timport urlConfig from '../../utils/urlConfig.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuserInfo: {},\n\t\t\t\tnewOrderNumber: '',\n\t\t\t\torderList: [],\n\t\t\t\tisLoading: false,\n\t\t\t\tisLoadingOrders: false,\n\t\t\t\tisRefreshing: false,\n\t\t\t\t// 查询相关\n\t\t\t\tshowQueryResult: false,\n\t\t\t\tisQuerying: false,\n\t\t\t\tqueryResult: null\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\n\n\t\t\t/**\n\t\t\t * 处理查询订单（基于shipping_detail表，支持模糊查询）\n\t\t\t */\n\t\t\tasync handleQueryOrder() {\n\t\t\t\tif (!this.newOrderNumber.trim()) {\n\t\t\t\t\tshowError('请输入订单号或关键词');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.isQuerying = true;\n\t\t\t\tthis.showQueryResult = true;\n\t\t\t\tthis.queryResult = null;\n\n\t\t\t\ttry {\n\t\t\t\t\tconst searchTerm = this.newOrderNumber.trim();\n\t\t\t\t\tconsole.log('开始模糊查询订单:', searchTerm);\n\t\t\t\t\tconst response = await this.queryOrderRequest(searchTerm);\n\n\t\t\t\t\tif (response.success && response.data) {\n\t\t\t\t\t\tthis.queryResult = response.data;\n\n\t\t\t\t\t\t// 显示查询结果信息\n\t\t\t\t\t\tif (Array.isArray(response.data)) {\n\t\t\t\t\t\t\tconsole.log(`模糊查询成功: 找到 ${response.data.length} 个匹配的订单`);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: `找到 ${response.data.length} 个匹配订单`,\n\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.log('精确查询成功:', this.queryResult);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '查询成功',\n\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.queryResult = null;\n\t\t\t\t\t\tconsole.log('未找到匹配的订单:', searchTerm);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '未找到匹配的订单',\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('查询订单失败:', error);\n\t\t\t\t\tthis.queryResult = null;\n\t\t\t\t\tshowError('查询失败: ' + error.message);\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isQuerying = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 关闭查询结果\n\t\t\t */\n\t\t\tcloseQueryResult() {\n\t\t\t\tthis.showQueryResult = false;\n\t\t\t\tthis.queryResult = null;\n\t\t\t\tthis.newOrderNumber = '';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 跳转到图片管理页面\n\t\t\t */\n\t\t\tgoToImageManage(orderNumber) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/imageManage/imageManage?orderNumber=${encodeURIComponent(orderNumber)}`,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('跳转到图片管理页面:', orderNumber);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\tshowError('页面跳转失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 加载历史订单\n\t\t\t */\n\t\t\tasync loadOrderHistory() {\n\t\t\t\tif (!this.userInfo.factory_name) {\n\t\t\t\t\tconsole.log('用户工厂名称为空，跳过加载订单历史');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.isLoadingOrders = true;\n\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('开始加载订单历史...');\n\t\t\t\t\tconst response = await this.getOrderHistoryRequest();\n\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tthis.orderList = response.data || [];\n\t\t\t\t\t\tconsole.log('订单历史加载成功:', this.orderList.length, '个订单');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('获取订单历史失败:', response.message);\n\t\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载订单历史失败:', error);\n\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\tshowError('加载订单历史失败');\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoadingOrders = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取订单历史请求\n\t\t\t */\n\t\t\tasync getOrderHistoryRequest() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tconst requestConfig = userManager.createAuthRequest({\n\t\t\t\t\t\turl: urlConfig.getApiUrl('/api/orders/history'),\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\ttimeout: 10000,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t\tresolve(res.data);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treject(new Error(`服务器错误 (${res.statusCode})`));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('获取订单历史请求失败:', err);\n\t\t\t\t\t\t\treject(new Error('网络连接失败'));\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tuni.request(requestConfig);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 查询订单请求\n\t\t\t */\n\t\t\tasync queryOrderRequest(orderNumber) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tconst requestConfig = userManager.createAuthRequest({\n\t\t\t\t\t\turl: urlConfig.getApiUrl(`/api/orders/query/${encodeURIComponent(orderNumber)}`),\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\ttimeout: 10000,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t\tresolve(res.data);\n\t\t\t\t\t\t\t} else if (res.statusCode === 404) {\n\t\t\t\t\t\t\t\t// 订单不存在\n\t\t\t\t\t\t\t\tresolve({ success: false, message: '订单不存在' });\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treject(new Error(`服务器错误 (${res.statusCode})`));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('查询订单请求失败:', err);\n\t\t\t\t\t\t\treject(new Error('网络连接失败'));\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tuni.request(requestConfig);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 下拉刷新\n\t\t\t */\n\t\t\tasync handleRefresh() {\n\t\t\t\tconsole.log('开始下拉刷新...');\n\t\t\t\tthis.isRefreshing = true;\n\t\t\t\ttry {\n\t\t\t\t\tawait this.loadOrderHistory();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('下拉刷新失败:', error);\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isRefreshing = false;\n\t\t\t\t\tconsole.log('下拉刷新完成');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理退出登录\n\t\t\t */\n\t\t\thandleLogout() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认退出',\n\t\t\t\t\tcontent: '确定要退出登录吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 使用用户管理器退出登录\n\t\t\t\t\t\t\tuserManager.logout();\n\n\t\t\t\t\t\t\t// 跳转到主页面\n\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\turl: '/pages/home/<USER>'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 格式化时间\n\t\t\t */\n\t\t\tformatTime(timeStr) {\n\t\t\t\tif (!timeStr) return '未知';\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst date = new Date(timeStr);\n\t\t\t\t\tconst now = new Date();\n\t\t\t\t\tconst diff = now - date;\n\t\t\t\t\t\n\t\t\t\t\tif (diff < 60000) { // 1分钟内\n\t\t\t\t\t\treturn '刚刚';\n\t\t\t\t\t} else if (diff < 3600000) { // 1小时内\n\t\t\t\t\t\treturn `${Math.floor(diff / 60000)}分钟前`;\n\t\t\t\t\t} else if (diff < 86400000) { // 1天内\n\t\t\t\t\t\treturn `${Math.floor(diff / 3600000)}小时前`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn date.toLocaleDateString();\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn '未知';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 初始化数据\n\t\t\t */\n\t\t\tinitData() {\n\t\t\t\t// 确保数据结构正确\n\t\t\t\tthis.orderList = [];\n\t\t\t\tthis.isLoadingOrders = false;\n\t\t\t\tthis.isRefreshing = false;\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tthis.newOrderNumber = '';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 初始化用户信息\n\t\t\t */\n\t\t\tinitUserInfo() {\n\t\t\t\t// 初始化数据\n\t\t\t\tthis.initData();\n\n\t\t\t\t// 使用用户管理器检查登录状态\n\t\t\t\tif (userManager.requireLogin()) {\n\t\t\t\t\tthis.userInfo = userManager.getUserInfo();\n\t\t\t\t\t// 延迟加载订单历史，确保页面完全渲染\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.loadOrderHistory();\n\t\t\t\t\t\t}, 100);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t// 如果未登录，requireLogin会自动跳转到登录页面\n\t\t\t}\n\t\t},\n\n\t\tonLoad() {\n\t\t\tthis.initUserInfo();\n\t\t},\n\n\t\tonShow() {\n\t\t\t// 页面显示时刷新订单列表\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tif (this.userInfo.factory_name) {\n\t\t\t\t\tthis.loadOrderHistory();\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\tonReady() {\n\t\t\t// 页面初次渲染完成\n\t\t\tconsole.log('订单页面渲染完成');\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.order-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t\tpadding: 20rpx;\n\t}\n\n\t.header {\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tbox-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\n\t}\n\n\t.factory-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex: 1;\n\t}\n\n\t.factory-icon {\n\t\tfont-size: 50rpx;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.factory-details {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.factory-name {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.welcome-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.logout-btn {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.logout-icon {\n\t\tfont-size: 36rpx;\n\t\tcolor: #ffffff;\n\t}\n\n\t.new-order-section {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.section-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.title-icon {\n\t\tfont-size: 36rpx;\n\t\tmargin-right: 15rpx;\n\t\tcolor: #667eea;\n\t}\n\n\t.title-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.order-count {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tmargin-left: 10rpx;\n\t}\n\n\t.input-group {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\n\t.order-input {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 0 25rpx;\n\t\tfont-size: 30rpx;\n\t\tcolor: #333333;\n\t\tbackground-color: #ffffff;\n\t}\n\n\t.order-input:focus {\n\t\tborder-color: #667eea;\n\t}\n\n\t.add-btn {\n\t\twidth: 120rpx;\n\t\theight: 80rpx;\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t\tborder: none;\n\t\tborder-radius: 12rpx;\n\t\tcolor: #ffffff;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.add-btn:disabled {\n\t\tbackground: #cccccc;\n\t}\n\n\t.query-btn {\n\t\twidth: 120rpx;\n\t\theight: 80rpx;\n\t\tbackground: linear-gradient(135deg, #28a745, #20c997);\n\t\tborder: none;\n\t\tborder-radius: 12rpx;\n\t\tcolor: #ffffff;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.query-btn:disabled {\n\t\tbackground: #cccccc;\n\t}\n\n\t.query-tip {\n\t\tmargin-top: 20rpx;\n\t\tpadding: 15rpx 20rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 10rpx;\n\t\tborder-left: 4rpx solid #667eea;\n\t}\n\n\t.tip-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #6c757d;\n\t\tline-height: 1.4;\n\t}\n\n\t/* 查询结果区域 */\n\t.query-result-section {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t\tborder: 2rpx solid #28a745;\n\t}\n\n\t.query-result-section .section-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.close-query {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder: 1rpx solid #dee2e6;\n\t}\n\n\t.close-icon {\n\t\tfont-size: 24rpx;\n\t\tcolor: #6c757d;\n\t\tfont-weight: bold;\n\t}\n\n\t.query-content {\n\t\tmin-height: 100rpx;\n\t}\n\n\t.query-order-item {\n\t\tborder: 2rpx solid #28a745;\n\t\tbackground: #f8fff9;\n\t}\n\n\t.query-success {\n\t\tanimation: fadeIn 0.3s ease-in;\n\t}\n\n\t.single-result {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.multiple-results {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.result-header {\n\t\tpadding: 20rpx 0;\n\t\tborder-bottom: 1rpx solid #e0e0e0;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.result-count {\n\t\tfont-size: 28rpx;\n\t\tcolor: #28a745;\n\t\tfont-weight: bold;\n\t}\n\n\t.query-list {\n\t\tmax-height: 400rpx;\n\t}\n\n\t.query-list .order-item {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.query-list .order-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.query-empty {\n\t\ttext-align: center;\n\t\tpadding: 60rpx 20rpx;\n\t\tcolor: #999999;\n\t}\n\n\t.query-empty .empty-icon {\n\t\tfont-size: 80rpx;\n\t\tdisplay: block;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.query-empty .empty-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666666;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.query-empty .empty-hint {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tdisplay: block;\n\t\tline-height: 1.5;\n\t}\n\n\t@keyframes fadeIn {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(20rpx);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.history-section {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tflex: 1;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.loading-section {\n\t\ttext-align: center;\n\t\tpadding: 60rpx 0;\n\t}\n\n\t.loading-spinner {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder: 4rpx solid #f3f3f3;\n\t\tborder-top: 4rpx solid #667eea;\n\t\tborder-radius: 50%;\n\t\tanimation: spin 1s linear infinite;\n\t\tmargin: 0 auto 20rpx;\n\t}\n\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999999;\n\t}\n\n\t.order-list {\n\t\tmax-height: 600rpx;\n\t}\n\n\t.order-item {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tpadding: 30rpx 0;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\ttransition: background-color 0.3s ease;\n\t\tmin-height: 120rpx;\n\t}\n\n\t.order-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.order-item:active {\n\t\tbackground-color: #f8f9ff;\n\t}\n\n\t.order-info {\n\t\tflex: 1;\n\t}\n\n\t.order-number {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 15rpx;\n\t}\n\n\t.order-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.order-value {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.order-time {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.order-time:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.time-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.time-value {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t}\n\n\t.order-images {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.images-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.images-value {\n\t\tfont-size: 24rpx;\n\t\tcolor: #007aff;\n\t\tfont-weight: bold;\n\t}\n\n\t.order-arrow {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.arrow {\n\t\tfont-size: 36rpx;\n\t\tcolor: #667eea;\n\t\tfont-weight: bold;\n\t}\n\n\t.empty-state {\n\t\ttext-align: center;\n\t\tpadding: 80rpx 0;\n\t}\n\n\t.empty-icon {\n\t\tfont-size: 80rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tdisplay: block;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666666;\n\t\tmargin-bottom: 15rpx;\n\t\tdisplay: block;\n\t}\n\n\t.empty-hint {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999999;\n\t\tdisplay: block;\n\t}\n\n\t@keyframes spin {\n\t\t0% { transform: rotate(0deg); }\n\t\t100% { transform: rotate(360deg); }\n\t}\n</style>\n", "import MiniProgramPage from 'D:/Desktop/Warehouse/front/pages/order/order.vue'\nwx.createPage(MiniProgramPage)"], "names": ["showError", "uni", "userManager", "urlConfig"], "mappings": ";;;;;AA6MC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU,CAAE;AAAA,MACZ,gBAAgB;AAAA,MAChB,WAAW,CAAE;AAAA,MACb,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,cAAc;AAAA;AAAA,MAEd,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,aAAa;AAAA,IACd;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAMR,MAAM,mBAAmB;AACxB,UAAI,CAAC,KAAK,eAAe,QAAQ;AAChCA,sBAAS,UAAC,YAAY;AACtB;AAAA,MACD;AAEA,WAAK,aAAa;AAClB,WAAK,kBAAkB;AACvB,WAAK,cAAc;AAEnB,UAAI;AACH,cAAM,aAAa,KAAK,eAAe,KAAI;AAC3CC,sBAAY,MAAA,MAAA,OAAA,gCAAA,aAAa,UAAU;AACnC,cAAM,WAAW,MAAM,KAAK,kBAAkB,UAAU;AAExD,YAAI,SAAS,WAAW,SAAS,MAAM;AACtC,eAAK,cAAc,SAAS;AAG5B,cAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AACjCA,0BAAAA,MAAA,MAAA,OAAA,gCAAY,cAAc,SAAS,KAAK,MAAM,SAAS;AACvDA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,MAAM,SAAS,KAAK,MAAM;AAAA,cACjC,MAAM;AAAA,cACN,UAAU;AAAA,YACX,CAAC;AAAA,iBACK;AACNA,0BAAA,MAAA,MAAA,OAAA,gCAAY,WAAW,KAAK,WAAW;AACvCA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACX,CAAC;AAAA,UACF;AAAA,eACM;AACN,eAAK,cAAc;AACnBA,wBAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,UAAU;AACnCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,2EAAc,WAAW,KAAK;AAC9B,aAAK,cAAc;AACnBD,sBAAAA,UAAU,WAAW,MAAM,OAAO;AAAA,MACnC,UAAU;AACT,aAAK,aAAa;AAAA,MACnB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,mBAAmB;AAClB,WAAK,kBAAkB;AACvB,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA,IAKD,gBAAgB,aAAa;AAC5BC,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,8CAA8C,mBAAmB,WAAW,CAAC;AAAA,QAClF,SAAS,MAAM;AACdA,wBAAY,MAAA,MAAA,OAAA,gCAAA,cAAc,WAAW;AAAA,QACrC;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,GAAG;AAC1BD,wBAAS,UAAC,QAAQ;AAAA,QACnB;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,mBAAmB;AACxB,UAAI,CAAC,KAAK,SAAS,cAAc;AAChCC,sBAAAA,MAAA,MAAA,OAAA,gCAAY,mBAAmB;AAC/B;AAAA,MACD;AAEA,WAAK,kBAAkB;AAEvB,UAAI;AACHA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,aAAa;AACzB,cAAM,WAAW,MAAM,KAAK;AAE5B,YAAI,SAAS,SAAS;AACrB,eAAK,YAAY,SAAS,QAAQ,CAAA;AAClCA,8BAAY,MAAA,OAAA,gCAAA,aAAa,KAAK,UAAU,QAAQ,KAAK;AAAA,eAC/C;AACNA,6EAAc,aAAa,SAAS,OAAO;AAC3C,eAAK,YAAY;QAClB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAChC,aAAK,YAAY;AACjBD,sBAAS,UAAC,UAAU;AAAA,MACrB,UAAU;AACT,aAAK,kBAAkB;AAAA,MACxB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,yBAAyB;AAC9B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,cAAM,gBAAgBE,kBAAW,YAAC,kBAAkB;AAAA,UACnD,KAAKC,gBAAAA,UAAU,UAAU,qBAAqB;AAAA,UAC9C,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,eAAe,KAAK;AAC3B,sBAAQ,IAAI,IAAI;AAAA,mBACV;AACN,qBAAO,IAAI,MAAM,UAAU,IAAI,UAAU,GAAG,CAAC;AAAA,YAC9C;AAAA,UACA;AAAA,UACD,MAAM,CAAC,QAAQ;AACdF,0BAAA,MAAA,MAAA,SAAA,gCAAc,eAAe,GAAG;AAChC,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QACD,CAAC;AAEDA,4BAAI,QAAQ,aAAa;AAAA,MAC1B,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,kBAAkB,aAAa;AACpC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,cAAM,gBAAgBC,kBAAW,YAAC,kBAAkB;AAAA,UACnD,KAAKC,gBAAS,UAAC,UAAU,qBAAqB,mBAAmB,WAAW,CAAC,EAAE;AAAA,UAC/E,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,eAAe,KAAK;AAC3B,sBAAQ,IAAI,IAAI;AAAA,uBACN,IAAI,eAAe,KAAK;AAElC,sBAAQ,EAAE,SAAS,OAAO,SAAS,QAAS,CAAA;AAAA,mBACtC;AACN,qBAAO,IAAI,MAAM,UAAU,IAAI,UAAU,GAAG,CAAC;AAAA,YAC9C;AAAA,UACA;AAAA,UACD,MAAM,CAAC,QAAQ;AACdF,0BAAc,MAAA,MAAA,SAAA,gCAAA,aAAa,GAAG;AAC9B,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QACD,CAAC;AAEDA,4BAAI,QAAQ,aAAa;AAAA,MAC1B,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,gBAAgB;AACrBA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,WAAW;AACvB,WAAK,eAAe;AACpB,UAAI;AACH,cAAM,KAAK;MACV,SAAO,OAAO;AACfA,2EAAc,WAAW,KAAK;AAAA,MAC/B,UAAU;AACT,aAAK,eAAe;AACpBA,sBAAAA,mDAAY,QAAQ;AAAA,MACrB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhBC,8BAAW,YAAC,OAAM;AAGlBD,0BAAAA,MAAI,SAAS;AAAA,cACZ,KAAK;AAAA,YACN,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW,SAAS;AACnB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,cAAM,MAAM,oBAAI;AAChB,cAAM,OAAO,MAAM;AAEnB,YAAI,OAAO,KAAO;AACjB,iBAAO;AAAA,mBACG,OAAO,MAAS;AAC1B,iBAAO,GAAG,KAAK,MAAM,OAAO,GAAK,CAAC;AAAA,mBACxB,OAAO,OAAU;AAC3B,iBAAO,GAAG,KAAK,MAAM,OAAO,IAAO,CAAC;AAAA,eAC9B;AACN,iBAAO,KAAK;QACb;AAAA,MACC,SAAO,OAAO;AACf,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW;AAEV,WAAK,YAAY;AACjB,WAAK,kBAAkB;AACvB,WAAK,eAAe;AACpB,WAAK,YAAY;AACjB,WAAK,iBAAiB;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe;AAEd,WAAK,SAAQ;AAGb,UAAIC,kBAAAA,YAAY,gBAAgB;AAC/B,aAAK,WAAWA,8BAAY;AAE5B,aAAK,UAAU,MAAM;AACpB,qBAAW,MAAM;AAChB,iBAAK,iBAAgB;AAAA,UACrB,GAAE,GAAG;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IAED;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,aAAY;AAAA,EACjB;AAAA,EAED,SAAS;AAER,SAAK,UAAU,MAAM;AACpB,UAAI,KAAK,SAAS,cAAc;AAC/B,aAAK,iBAAgB;AAAA,MACtB;AAAA,IACD,CAAC;AAAA,EACD;AAAA,EAED,UAAU;AAETD,kBAAAA,MAAY,MAAA,OAAA,gCAAA,UAAU;AAAA,EACvB;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpfD,GAAG,WAAW,eAAe;"}