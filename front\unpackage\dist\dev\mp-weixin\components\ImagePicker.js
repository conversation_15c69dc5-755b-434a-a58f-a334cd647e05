"use strict";
const common_vendor = require("../common/vendor.js");
const utils_helpers = require("../utils/helpers.js");
const _sfc_main = {
  name: "ImagePicker",
  props: {
    // 当前选中的图片数组 (Vue 3 v-model)
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedImages: []
    };
  },
  watch: {
    modelValue: {
      handler(newVal) {
        this.selectedImages = Array.isArray(newVal) ? [...newVal] : [];
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 拍照
     */
    takePhoto() {
      common_vendor.index.__f__("log", "at components/ImagePicker.vue:76", "📷 点击拍照按钮");
      const remainingCount = 9 - this.selectedImages.length;
      if (remainingCount <= 0) {
        utils_helpers.showError("最多只能选择9张图片");
        return;
      }
      common_vendor.index.chooseImage({
        count: 1,
        // 拍照只能一张
        sourceType: ["camera"],
        success: (res) => {
          common_vendor.index.__f__("log", "at components/ImagePicker.vue:95", "📷 拍照成功:", res);
          this.handleImageSelect(res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/ImagePicker.vue:99", "📷 拍照失败:", err);
          utils_helpers.showError("拍照失败");
        }
      });
    },
    /**
     * 从相册选择图片
     */
    chooseImage() {
      common_vendor.index.__f__("log", "at components/ImagePicker.vue:110", "🖼️ 点击相册按钮");
      const remainingCount = 9 - this.selectedImages.length;
      if (remainingCount <= 0) {
        utils_helpers.showError("最多只能选择9张图片");
        return;
      }
      common_vendor.index.chooseImage({
        count: remainingCount,
        // 根据剩余数量动态设置
        sourceType: ["album"],
        success: (res) => {
          common_vendor.index.__f__("log", "at components/ImagePicker.vue:121", "🖼️ 相册选择成功:", res);
          this.handleImageSelect(res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/ImagePicker.vue:125", "🖼️ 选择图片失败:", err);
          utils_helpers.showError("选择图片失败");
        }
      });
    },
    /**
     * 处理图片选择结果
     */
    handleImageSelect(res) {
      common_vendor.index.__f__("log", "at components/ImagePicker.vue:135", "🔄 处理图片选择结果:", res);
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        const newImages = [...this.selectedImages, ...res.tempFilePaths];
        if (newImages.length > 9) {
          utils_helpers.showError("最多只能选择9张图片");
          return;
        }
        this.selectedImages = newImages;
        common_vendor.index.__f__("log", "at components/ImagePicker.vue:147", "✅ 选择图片成功，当前共:", this.selectedImages.length, "张");
        this.$emit("update:modelValue", this.selectedImages);
        this.$emit("change", this.selectedImages);
        common_vendor.index.__f__("log", "at components/ImagePicker.vue:152", "📤 已发送事件到父组件");
        utils_helpers.showSuccess(`成功添加 ${res.tempFilePaths.length} 张图片`);
      } else {
        common_vendor.index.__f__("log", "at components/ImagePicker.vue:156", "❌ 图片选择失败，没有获取到文件路径");
        utils_helpers.showError("图片选择失败");
      }
    },
    /**
     * 移除指定索引的图片
     */
    removeImage(index) {
      common_vendor.index.__f__("log", "at components/ImagePicker.vue:165", "🗑️ 移除图片，索引:", index);
      this.selectedImages.splice(index, 1);
      this.$emit("update:modelValue", this.selectedImages);
      this.$emit("change", this.selectedImages);
      utils_helpers.showSuccess("图片已移除");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedImages.length > 0
  }, $data.selectedImages.length > 0 ? common_vendor.e({
    b: common_vendor.f($data.selectedImages, (image, index, i0) => {
      return {
        a: image,
        b: common_vendor.o(($event) => $options.removeImage(index), index),
        c: common_vendor.t(index + 1),
        d: index
      };
    }),
    c: $data.selectedImages.length < 9
  }, $data.selectedImages.length < 9 ? {
    d: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    e: common_vendor.t($data.selectedImages.length)
  }) : {}, {
    f: common_vendor.o((...args) => $options.takePhoto && $options.takePhoto(...args)),
    g: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8ce556f9"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/ImagePicker.js.map
