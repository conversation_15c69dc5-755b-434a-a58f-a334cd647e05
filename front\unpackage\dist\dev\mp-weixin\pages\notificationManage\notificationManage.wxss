
.container.data-v-0aab7bd3 {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
}
.header.data-v-0aab7bd3 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
}
.title.data-v-0aab7bd3 {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
}
.refresh-btn.data-v-0aab7bd3 {
		background: #667eea;
		color: white;
		border: none;
		border-radius: 8rpx;
		padding: 16rpx 32rpx;
		font-size: 28rpx;
}
.stats-section.data-v-0aab7bd3 {
		display: flex;
		justify-content: space-between;
		margin-bottom: 30rpx;
}
.stat-card.data-v-0aab7bd3 {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		text-align: center;
		flex: 1;
		margin: 0 10rpx;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.stat-number.data-v-0aab7bd3 {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: #667eea;
		margin-bottom: 10rpx;
}
.stat-label.data-v-0aab7bd3 {
		font-size: 24rpx;
		color: #666;
}
.filter-section.data-v-0aab7bd3 {
		display: flex;
		margin-bottom: 30rpx;
}
.picker-item.data-v-0aab7bd3 {
		background: white;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-right: 20rpx;
		border: 2rpx solid #e0e0e0;
		font-size: 28rpx;
}
.notification-list.data-v-0aab7bd3 {
		margin-bottom: 30rpx;
}
.notification-item.data-v-0aab7bd3 {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.notification-header.data-v-0aab7bd3 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
}
.order-no.data-v-0aab7bd3 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.status-badge.data-v-0aab7bd3 {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		color: white;
}
.status-pending.data-v-0aab7bd3 { background: #ffa500;
}
.status-processing.data-v-0aab7bd3 { background: #1890ff;
}
.status-sent.data-v-0aab7bd3 { background: #52c41a;
}
.status-failed.data-v-0aab7bd3 { background: #ff4d4f;
}
.notification-content text.data-v-0aab7bd3 {
		display: block;
		font-size: 26rpx;
		color: #666;
		margin-bottom: 8rpx;
}
.error-message text.data-v-0aab7bd3 {
		color: #ff4d4f;
		font-size: 24rpx;
}
.notification-actions.data-v-0aab7bd3 {
		display: flex;
		justify-content: flex-end;
		margin-top: 20rpx;
}
.action-btn.data-v-0aab7bd3 {
		border: none;
		border-radius: 8rpx;
		padding: 12rpx 24rpx;
		font-size: 24rpx;
		margin-left: 16rpx;
}
.retry-btn.data-v-0aab7bd3 {
		background: #1890ff;
		color: white;
}
.delete-btn.data-v-0aab7bd3 {
		background: #ff4d4f;
		color: white;
}
.pagination.data-v-0aab7bd3 {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 30rpx;
}
.page-btn.data-v-0aab7bd3 {
		background: #667eea;
		color: white;
		border: none;
		border-radius: 8rpx;
		padding: 16rpx 32rpx;
		font-size: 28rpx;
		margin: 0 20rpx;
}
.page-btn.data-v-0aab7bd3:disabled {
		background: #ccc;
}
.page-info.data-v-0aab7bd3 {
		font-size: 28rpx;
		color: #666;
}
.action-section.data-v-0aab7bd3 {
		display: flex;
		justify-content: center;
}
.test-btn.data-v-0aab7bd3, .cleanup-btn.data-v-0aab7bd3 {
		background: #52c41a;
		color: white;
		border: none;
		border-radius: 8rpx;
		padding: 20rpx 40rpx;
		font-size: 28rpx;
		margin: 0 20rpx;
}
.cleanup-btn.data-v-0aab7bd3 {
		background: #fa8c16;
}
.empty-state.data-v-0aab7bd3 {
		text-align: center;
		padding: 80rpx;
		color: #999;
}
	
	/* 弹窗样式 */
.modal-overlay.data-v-0aab7bd3 {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0,0,0,0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
}
.modal-content.data-v-0aab7bd3 {
		background: white;
		border-radius: 16rpx;
		width: 80%;
		max-width: 600rpx;
}
.modal-header.data-v-0aab7bd3 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 2rpx solid #f0f0f0;
}
.modal-title.data-v-0aab7bd3 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.modal-close.data-v-0aab7bd3 {
		font-size: 48rpx;
		color: #999;
		cursor: pointer;
}
.modal-body.data-v-0aab7bd3 {
		padding: 30rpx;
}
.input-group.data-v-0aab7bd3 {
		margin-bottom: 30rpx;
}
.input-label.data-v-0aab7bd3 {
		display: block;
		font-size: 28rpx;
		color: #666;
		margin-bottom: 16rpx;
}
.input-field.data-v-0aab7bd3 {
		width: 100%;
		padding: 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 8rpx;
		font-size: 28rpx;
}
.modal-footer.data-v-0aab7bd3 {
		display: flex;
		justify-content: flex-end;
		padding: 30rpx;
		border-top: 2rpx solid #f0f0f0;
}
.cancel-btn.data-v-0aab7bd3, .confirm-btn.data-v-0aab7bd3 {
		border: none;
		border-radius: 8rpx;
		padding: 16rpx 32rpx;
		font-size: 28rpx;
		margin-left: 20rpx;
}
.cancel-btn.data-v-0aab7bd3 {
		background: #f0f0f0;
		color: #666;
}
.confirm-btn.data-v-0aab7bd3 {
		background: #667eea;
		color: white;
}
