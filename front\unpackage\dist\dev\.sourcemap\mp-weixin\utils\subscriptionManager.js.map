{"version": 3, "file": "subscriptionManager.js", "sources": ["utils/subscriptionManager.js"], "sourcesContent": ["/**\n * 订阅消息管理器\n * 处理微信小程序订阅消息相关功能\n */\n\nimport apiService from './apiService.js';\n\nclass SubscriptionManager {\n    constructor() {\n        this.templateId = 'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8'; // 订单进度提醒模板\n        this.openid = null;\n        this.subscribed = false;\n    }\n\n    /**\n     * 获取用户openid\n     */\n    async getOpenid() {\n        try {\n            // 如果已有openid，直接返回\n            if (this.openid) {\n                return this.openid;\n            }\n\n            // 从缓存中获取\n            const cachedOpenid = uni.getStorageSync('user_openid');\n            if (cachedOpenid) {\n                this.openid = cachedOpenid;\n                return this.openid;\n            }\n\n            // 通过微信登录获取code\n            const loginResult = await uni.login();\n            if (!loginResult.code) {\n                throw new Error('获取微信登录code失败');\n            }\n\n            // 发送code到后端换取openid\n            const response = await apiService.post('/subscription/get-openid', {\n                code: loginResult.code\n            });\n\n            if (response.success && response.data.openid) {\n                this.openid = response.data.openid;\n                // 缓存openid\n                uni.setStorageSync('user_openid', this.openid);\n                console.log('✅ 获取openid成功');\n                return this.openid;\n            } else {\n                throw new Error('获取openid失败');\n            }\n        } catch (error) {\n            console.error('❌ 获取openid失败:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * 申请订阅消息权限\n     */\n    async requestSubscribeMessage() {\n        try {\n            console.log('🔔 申请订阅消息权限...');\n\n            // 检查是否在微信小程序环境\n            // #ifdef MP-WEIXIN\n            const result = await uni.requestSubscribeMessage({\n                tmplIds: [this.templateId]\n            });\n\n            console.log('订阅结果:', result);\n\n            // 获取openid\n            const openid = await this.getOpenid();\n\n            // 保存订阅状态到后端\n            if (result[this.templateId] === 'accept') {\n                await this.saveSubscriptionStatus(result, openid);\n                this.subscribed = true;\n                uni.showToast({\n                    title: '订阅成功',\n                    icon: 'success'\n                });\n                return true;\n            } else {\n                console.log('用户拒绝订阅或其他状态:', result[this.templateId]);\n                uni.showToast({\n                    title: '订阅被拒绝',\n                    icon: 'none'\n                });\n                return false;\n            }\n            // #endif\n\n            // #ifndef MP-WEIXIN\n            console.log('⚠️ 非微信小程序环境，跳过订阅消息申请');\n            return false;\n            // #endif\n\n        } catch (error) {\n            console.error('❌ 申请订阅消息失败:', error);\n            uni.showToast({\n                title: '订阅申请失败',\n                icon: 'none'\n            });\n            return false;\n        }\n    }\n\n    /**\n     * 保存订阅状态到后端\n     */\n    async saveSubscriptionStatus(subscribeResult, openid) {\n        try {\n            const response = await apiService.post('/subscription/save', {\n                subscribeResult: subscribeResult,\n                openid: openid\n            });\n\n            if (response.success) {\n                console.log('✅ 订阅状态保存成功');\n            } else {\n                console.error('❌ 订阅状态保存失败:', response.message);\n            }\n        } catch (error) {\n            console.error('❌ 保存订阅状态异常:', error);\n        }\n    }\n\n    /**\n     * 检查用户订阅状态\n     */\n    async checkSubscriptionStatus() {\n        try {\n            const response = await apiService.get('/subscription/status');\n            \n            if (response.success) {\n                this.subscribed = response.data.subscribed;\n                console.log('📋 用户订阅状态:', this.subscribed ? '已订阅' : '未订阅');\n                return this.subscribed;\n            } else {\n                console.error('❌ 获取订阅状态失败:', response.message);\n                return false;\n            }\n        } catch (error) {\n            console.error('❌ 检查订阅状态异常:', error);\n            return false;\n        }\n    }\n\n    /**\n     * 获取消息发送历史\n     */\n    async getMessageHistory(page = 1, limit = 20) {\n        try {\n            const response = await apiService.get(`/subscription/message-history?page=${page}&limit=${limit}`);\n            \n            if (response.success) {\n                return response.data;\n            } else {\n                console.error('❌ 获取消息历史失败:', response.message);\n                return null;\n            }\n        } catch (error) {\n            console.error('❌ 获取消息历史异常:', error);\n            return null;\n        }\n    }\n\n    /**\n     * 手动发送测试通知（仅用于测试）\n     */\n    async sendTestNotification(orderNo) {\n        try {\n            const response = await apiService.post('/subscription/send-notification', {\n                orderNo: orderNo\n            });\n\n            if (response.success) {\n                uni.showToast({\n                    title: '测试通知发送成功',\n                    icon: 'success'\n                });\n                return true;\n            } else {\n                uni.showToast({\n                    title: response.message || '发送失败',\n                    icon: 'none'\n                });\n                return false;\n            }\n        } catch (error) {\n            console.error('❌ 发送测试通知失败:', error);\n            uni.showToast({\n                title: '发送失败',\n                icon: 'none'\n            });\n            return false;\n        }\n    }\n\n    /**\n     * 在适当的时机提示用户订阅\n     */\n    async promptSubscribeIfNeeded() {\n        try {\n            // 检查当前订阅状态\n            const isSubscribed = await this.checkSubscriptionStatus();\n            \n            if (!isSubscribed) {\n                // 显示订阅提示\n                uni.showModal({\n                    title: '消息通知',\n                    content: '开启消息通知，及时了解订单图片上传状态',\n                    confirmText: '开启通知',\n                    cancelText: '暂不开启',\n                    success: async (res) => {\n                        if (res.confirm) {\n                            await this.requestSubscribeMessage();\n                        }\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('❌ 提示订阅失败:', error);\n        }\n    }\n\n    /**\n     * 清除缓存的openid（用于调试）\n     */\n    clearOpenidCache() {\n        this.openid = null;\n        uni.removeStorageSync('user_openid');\n        console.log('🗑️ 已清除openid缓存');\n    }\n}\n\n// 创建单例实例\nconst subscriptionManager = new SubscriptionManager();\n\nexport default subscriptionManager;\n"], "names": ["uni", "apiService"], "mappings": ";;;AAOA,MAAM,oBAAoB;AAAA,EACtB,cAAc;AACV,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,YAAY;AACd,QAAI;AAEA,UAAI,KAAK,QAAQ;AACb,eAAO,KAAK;AAAA,MACf;AAGD,YAAM,eAAeA,cAAAA,MAAI,eAAe,aAAa;AACrD,UAAI,cAAc;AACd,aAAK,SAAS;AACd,eAAO,KAAK;AAAA,MACf;AAGD,YAAM,cAAc,MAAMA,oBAAI;AAC9B,UAAI,CAAC,YAAY,MAAM;AACnB,cAAM,IAAI,MAAM,cAAc;AAAA,MACjC;AAGD,YAAM,WAAW,MAAMC,4BAAW,KAAK,4BAA4B;AAAA,QAC/D,MAAM,YAAY;AAAA,MAClC,CAAa;AAED,UAAI,SAAS,WAAW,SAAS,KAAK,QAAQ;AAC1C,aAAK,SAAS,SAAS,KAAK;AAE5BD,sBAAAA,MAAI,eAAe,eAAe,KAAK,MAAM;AAC7CA,sBAAAA,MAAY,MAAA,OAAA,sCAAA,cAAc;AAC1B,eAAO,KAAK;AAAA,MAC5B,OAAmB;AACH,cAAM,IAAI,MAAM,YAAY;AAAA,MAC/B;AAAA,IACJ,SAAQ,OAAO;AACZA,oBAAA,MAAA,MAAA,SAAA,sCAAc,iBAAiB,KAAK;AACpC,YAAM;AAAA,IACT;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,0BAA0B;AAC5B,QAAI;AACAA,oBAAAA,MAAA,MAAA,OAAA,sCAAY,gBAAgB;AAI5B,YAAM,SAAS,MAAMA,cAAG,MAAC,wBAAwB;AAAA,QAC7C,SAAS,CAAC,KAAK,UAAU;AAAA,MACzC,CAAa;AAEDA,oBAAA,MAAA,MAAA,OAAA,sCAAY,SAAS,MAAM;AAG3B,YAAM,SAAS,MAAM,KAAK;AAG1B,UAAI,OAAO,KAAK,UAAU,MAAM,UAAU;AACtC,cAAM,KAAK,uBAAuB,QAAQ,MAAM;AAChD,aAAK,aAAa;AAClBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAC1B,CAAiB;AACD,eAAO;AAAA,MACvB,OAAmB;AACHA,4BAAA,MAAA,OAAA,sCAAY,gBAAgB,OAAO,KAAK,UAAU,CAAC;AACnDA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAC1B,CAAiB;AACD,eAAO;AAAA,MACV;AAAA,IAQJ,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,uCAAA,eAAe,KAAK;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,MACtB,CAAa;AACD,aAAO;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,uBAAuB,iBAAiB,QAAQ;AAClD,QAAI;AACA,YAAM,WAAW,MAAMC,4BAAW,KAAK,sBAAsB;AAAA,QACzD;AAAA,QACA;AAAA,MAChB,CAAa;AAED,UAAI,SAAS,SAAS;AAClBD,sBAAAA,MAAA,MAAA,OAAA,uCAAY,YAAY;AAAA,MACxC,OAAmB;AACHA,sBAAc,MAAA,MAAA,SAAA,uCAAA,eAAe,SAAS,OAAO;AAAA,MAChD;AAAA,IACJ,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,uCAAA,eAAe,KAAK;AAAA,IACrC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,0BAA0B;AAC5B,QAAI;AACA,YAAM,WAAW,MAAMC,iBAAAA,WAAW,IAAI,sBAAsB;AAE5D,UAAI,SAAS,SAAS;AAClB,aAAK,aAAa,SAAS,KAAK;AAChCD,4BAAA,MAAA,OAAA,uCAAY,cAAc,KAAK,aAAa,QAAQ,KAAK;AACzD,eAAO,KAAK;AAAA,MAC5B,OAAmB;AACHA,sBAAc,MAAA,MAAA,SAAA,uCAAA,eAAe,SAAS,OAAO;AAC7C,eAAO;AAAA,MACV;AAAA,IACJ,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,uCAAA,eAAe,KAAK;AAClC,aAAO;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,kBAAkB,OAAO,GAAG,QAAQ,IAAI;AAC1C,QAAI;AACA,YAAM,WAAW,MAAMC,iBAAU,WAAC,IAAI,sCAAsC,IAAI,UAAU,KAAK,EAAE;AAEjG,UAAI,SAAS,SAAS;AAClB,eAAO,SAAS;AAAA,MAChC,OAAmB;AACHD,sBAAc,MAAA,MAAA,SAAA,uCAAA,eAAe,SAAS,OAAO;AAC7C,eAAO;AAAA,MACV;AAAA,IACJ,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,uCAAA,eAAe,KAAK;AAClC,aAAO;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,qBAAqB,SAAS;AAChC,QAAI;AACA,YAAM,WAAW,MAAMC,4BAAW,KAAK,mCAAmC;AAAA,QACtE;AAAA,MAChB,CAAa;AAED,UAAI,SAAS,SAAS;AAClBD,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QAC1B,CAAiB;AACD,eAAO;AAAA,MACvB,OAAmB;AACHA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO,SAAS,WAAW;AAAA,UAC3B,MAAM;AAAA,QAC1B,CAAiB;AACD,eAAO;AAAA,MACV;AAAA,IACJ,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,uCAAA,eAAe,KAAK;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,MACtB,CAAa;AACD,aAAO;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,0BAA0B;AAC5B,QAAI;AAEA,YAAM,eAAe,MAAM,KAAK;AAEhC,UAAI,CAAC,cAAc;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,OAAO,QAAQ;AACpB,gBAAI,IAAI,SAAS;AACb,oBAAM,KAAK;YACd;AAAA,UACJ;AAAA,QACrB,CAAiB;AAAA,MACJ;AAAA,IACJ,SAAQ,OAAO;AACZA,oBAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,KAAK;AAAA,IACnC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB;AACf,SAAK,SAAS;AACdA,wBAAI,kBAAkB,aAAa;AACnCA,kBAAAA,MAAY,MAAA,OAAA,uCAAA,iBAAiB;AAAA,EAChC;AACL;AAGK,MAAC,sBAAsB,IAAI,oBAAmB;;"}