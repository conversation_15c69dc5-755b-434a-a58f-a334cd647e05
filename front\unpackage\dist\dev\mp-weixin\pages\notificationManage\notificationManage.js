"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_apiService = require("../../utils/apiService.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  data() {
    return {
      stats: {},
      notifications: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0
      },
      statusIndex: 0,
      statusOptions: ["全部", "PENDING", "PROCESSING", "SENT", "FAILED"],
      typeIndex: 0,
      typeOptions: ["全部", "ORDER_GENERATED", "IMAGE_UPLOADED"],
      showTestModal: false,
      testForm: {
        order_no: "",
        receiver: "",
        follower: ""
      }
    };
  },
  onLoad() {
    this.loadNotificationStats();
    this.loadNotifications();
  },
  methods: {
    /**
     * 加载通知统计
     */
    async loadNotificationStats() {
      try {
        const response = await utils_apiService.apiService.get("/notifications/status");
        if (response.success) {
          this.stats = response.data.queue;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/notificationManage/notificationManage.vue:177", "加载通知统计失败:", error);
      }
    },
    /**
     * 加载通知列表
     */
    async loadNotifications() {
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        const params = new URLSearchParams({
          page: this.pagination.page,
          limit: this.pagination.limit
        });
        if (this.statusIndex > 0) {
          params.append("status", this.statusOptions[this.statusIndex]);
        }
        if (this.typeIndex > 0) {
          params.append("notification_type", this.typeOptions[this.typeIndex]);
        }
        const response = await utils_apiService.apiService.get(`/notifications/queue?${params.toString()}`);
        if (response.success) {
          this.notifications = response.data.notifications;
          this.pagination = response.data.pagination;
        } else {
          utils_helpers.showError(response.message || "加载失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/notificationManage/notificationManage.vue:210", "加载通知列表失败:", error);
        utils_helpers.showError("加载失败");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 刷新数据
     */
    async refreshData() {
      await this.loadNotificationStats();
      await this.loadNotifications();
      utils_helpers.showSuccess("刷新成功");
    },
    /**
     * 状态筛选变更
     */
    onStatusChange(e) {
      this.statusIndex = e.detail.value;
      this.pagination.page = 1;
      this.loadNotifications();
    },
    /**
     * 类型筛选变更
     */
    onTypeChange(e) {
      this.typeIndex = e.detail.value;
      this.pagination.page = 1;
      this.loadNotifications();
    },
    /**
     * 上一页
     */
    prevPage() {
      if (this.pagination.page > 1) {
        this.pagination.page--;
        this.loadNotifications();
      }
    },
    /**
     * 下一页
     */
    nextPage() {
      if (this.pagination.page < this.pagination.totalPages) {
        this.pagination.page++;
        this.loadNotifications();
      }
    },
    /**
     * 重试通知
     */
    async retryNotification(id) {
      try {
        common_vendor.index.showLoading({ title: "重试中..." });
        const response = await utils_apiService.apiService.post(`/notifications/retry/${id}`);
        if (response.success) {
          utils_helpers.showSuccess("重试成功");
          this.loadNotifications();
        } else {
          utils_helpers.showError(response.message || "重试失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/notificationManage/notificationManage.vue:279", "重试通知失败:", error);
        utils_helpers.showError("重试失败");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 删除通知
     */
    async deleteNotification(id) {
      try {
        const confirmed = await new Promise((resolve) => {
          common_vendor.index.showModal({
            title: "确认删除",
            content: "确定要删除这条通知吗？",
            success: (res) => resolve(res.confirm)
          });
        });
        if (!confirmed)
          return;
        common_vendor.index.showLoading({ title: "删除中..." });
        const response = await utils_apiService.apiService.delete(`/notifications/${id}`);
        if (response.success) {
          utils_helpers.showSuccess("删除成功");
          this.loadNotifications();
        } else {
          utils_helpers.showError(response.message || "删除失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/notificationManage/notificationManage.vue:311", "删除通知失败:", error);
        utils_helpers.showError("删除失败");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 清理已发送通知
     */
    async cleanupNotifications() {
      try {
        const confirmed = await new Promise((resolve) => {
          common_vendor.index.showModal({
            title: "确认清理",
            content: "确定要清理7天前的已发送通知吗？",
            success: (res) => resolve(res.confirm)
          });
        });
        if (!confirmed)
          return;
        common_vendor.index.showLoading({ title: "清理中..." });
        const response = await utils_apiService.apiService.post("/notifications/cleanup", { days: 7 });
        if (response.success) {
          utils_helpers.showSuccess(`清理完成，删除了 ${response.data.deletedCount} 条记录`);
          this.refreshData();
        } else {
          utils_helpers.showError(response.message || "清理失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/notificationManage/notificationManage.vue:343", "清理通知失败:", error);
        utils_helpers.showError("清理失败");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 触发测试通知
     */
    async triggerTestNotification() {
      if (!this.testForm.order_no || !this.testForm.receiver) {
        utils_helpers.showError("请填写订单号和接收方");
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "发送中..." });
        const response = await utils_apiService.apiService.post("/notifications/trigger-order-notification", this.testForm);
        if (response.success) {
          utils_helpers.showSuccess("测试通知已发送");
          this.showTestModal = false;
          this.testForm = { order_no: "", receiver: "", follower: "" };
          setTimeout(() => {
            this.refreshData();
          }, 1e3);
        } else {
          utils_helpers.showError(response.message || "发送失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/notificationManage/notificationManage.vue:374", "发送测试通知失败:", error);
        utils_helpers.showError("发送失败");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
      const classMap = {
        "PENDING": "status-pending",
        "PROCESSING": "status-processing",
        "SENT": "status-sent",
        "FAILED": "status-failed"
      };
      return classMap[status] || "status-unknown";
    },
    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const textMap = {
        "PENDING": "待处理",
        "PROCESSING": "处理中",
        "SENT": "已发送",
        "FAILED": "失败"
      };
      return textMap[status] || status;
    },
    /**
     * 获取类型文本
     */
    getTypeText(type) {
      const textMap = {
        "ORDER_GENERATED": "订单生成",
        "IMAGE_UPLOADED": "图片上传"
      };
      return textMap[type] || type;
    },
    /**
     * 格式化时间
     */
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      const date = new Date(timeStr);
      return date.toLocaleString("zh-CN");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c;
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args)),
    b: common_vendor.t($data.stats.total || 0),
    c: common_vendor.t(((_a = $data.stats.byStatus) == null ? void 0 : _a.PENDING) || 0),
    d: common_vendor.t(((_b = $data.stats.byStatus) == null ? void 0 : _b.SENT) || 0),
    e: common_vendor.t(((_c = $data.stats.byStatus) == null ? void 0 : _c.FAILED) || 0),
    f: common_vendor.t($data.statusOptions[$data.statusIndex]),
    g: common_vendor.o((...args) => $options.onStatusChange && $options.onStatusChange(...args)),
    h: $data.statusIndex,
    i: $data.statusOptions,
    j: common_vendor.t($data.typeOptions[$data.typeIndex]),
    k: common_vendor.o((...args) => $options.onTypeChange && $options.onTypeChange(...args)),
    l: $data.typeIndex,
    m: $data.typeOptions,
    n: common_vendor.f($data.notifications, (notification, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(notification.order_no),
        b: common_vendor.t($options.getStatusText(notification.status)),
        c: common_vendor.n($options.getStatusClass(notification.status)),
        d: common_vendor.t(notification.receiver),
        e: common_vendor.t(notification.follower || "未指定"),
        f: common_vendor.t($options.getTypeText(notification.notification_type)),
        g: common_vendor.t($options.formatTime(notification.created_time)),
        h: notification.processed_at
      }, notification.processed_at ? {
        i: common_vendor.t($options.formatTime(notification.processed_at))
      } : {}, {
        j: notification.retry_count > 0
      }, notification.retry_count > 0 ? {
        k: common_vendor.t(notification.retry_count)
      } : {}, {
        l: notification.error_message
      }, notification.error_message ? {
        m: common_vendor.t(notification.error_message)
      } : {}, {
        n: notification.status === "FAILED"
      }, notification.status === "FAILED" ? {
        o: common_vendor.o(($event) => $options.retryNotification(notification.id), notification.id)
      } : {}, {
        p: common_vendor.o(($event) => $options.deleteNotification(notification.id), notification.id),
        q: notification.id
      });
    }),
    o: $data.notifications.length === 0
  }, $data.notifications.length === 0 ? {} : {}, {
    p: $data.pagination.totalPages > 1
  }, $data.pagination.totalPages > 1 ? {
    q: common_vendor.o((...args) => $options.prevPage && $options.prevPage(...args)),
    r: $data.pagination.page <= 1,
    s: common_vendor.t($data.pagination.page),
    t: common_vendor.t($data.pagination.totalPages),
    v: common_vendor.o((...args) => $options.nextPage && $options.nextPage(...args)),
    w: $data.pagination.page >= $data.pagination.totalPages
  } : {}, {
    x: common_vendor.o(($event) => $data.showTestModal = true),
    y: common_vendor.o((...args) => $options.cleanupNotifications && $options.cleanupNotifications(...args)),
    z: $data.showTestModal
  }, $data.showTestModal ? {
    A: common_vendor.o(($event) => $data.showTestModal = false),
    B: $data.testForm.order_no,
    C: common_vendor.o(($event) => $data.testForm.order_no = $event.detail.value),
    D: $data.testForm.receiver,
    E: common_vendor.o(($event) => $data.testForm.receiver = $event.detail.value),
    F: $data.testForm.follower,
    G: common_vendor.o(($event) => $data.testForm.follower = $event.detail.value),
    H: common_vendor.o(($event) => $data.showTestModal = false),
    I: common_vendor.o((...args) => $options.triggerTestNotification && $options.triggerTestNotification(...args)),
    J: common_vendor.o(() => {
    }),
    K: common_vendor.o(($event) => $data.showTestModal = false)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0aab7bd3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/notificationManage/notificationManage.js.map
