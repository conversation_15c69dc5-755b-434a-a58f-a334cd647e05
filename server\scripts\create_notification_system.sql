-- 订单生成通知系统数据库脚本
-- 数据库: identify

USE identify;

-- 创建通知队列表
CREATE TABLE IF NOT EXISTS notification_queue (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    trigger_type VARCHAR(50) NOT NULL COMMENT '触发类型：ORDER_CREATED, IMAGE_UPLOADED',
    order_no VARCHAR(100) NOT NULL COMMENT '订单号',
    receiver VARCHAR(100) COMMENT '接收方（工厂名称）',
    follower VARCHAR(100) COMMENT '跟单员',
    created_at DATETIME COMMENT '订单创建时间',
    notification_type VARCHAR(50) NOT NULL COMMENT '通知类型：ORDER_GENERATED, IMAGE_UPLOADED',
    status ENUM('PENDING', 'PROCESSING', 'SENT', 'FAILED') DEFAULT 'PENDING' COMMENT '处理状态',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    error_message TEXT COMMENT '错误信息',
    processed_at DATETIME COMMENT '处理时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_status (status),
    INDEX idx_order_no (order_no),
    INDEX idx_trigger_type (trigger_type),
    INDEX idx_notification_type (notification_type),
    INDEX idx_created_time (created_time),
    INDEX idx_retry_count (retry_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知队列表';

-- 创建订单生成通知触发器
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_shipping_detail_insert_notification
AFTER INSERT ON shipping_detail
FOR EACH ROW
BEGIN
    -- 插入通知任务到队列表
    INSERT INTO notification_queue (
        trigger_type,
        order_no,
        receiver,
        follower,
        created_at,
        notification_type,
        status,
        created_time
    ) VALUES (
        'ORDER_CREATED',
        NEW.order_no,
        NEW.receiver,
        NEW.follower,
        NEW.created_at,
        'ORDER_GENERATED',
        'PENDING',
        NOW()
    );
END$$
DELIMITER ;

-- 查看表结构
DESCRIBE notification_queue;

-- 查看触发器
SHOW TRIGGERS LIKE 'tr_shipping_detail_insert_notification';

-- 查看初始数据
SELECT COUNT(*) as notification_queue_count FROM notification_queue;
