{"version": 3, "file": "subscriptionTest.js", "sources": ["pages/subscriptionTest/subscriptionTest.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3Vic2NyaXB0aW9uVGVzdC9zdWJzY3JpcHRpb25UZXN0LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"title\">订阅消息测试</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"content\">\n\t\t\t<!-- 订阅状态 -->\n\t\t\t<view class=\"status-card\">\n\t\t\t\t<view class=\"status-title\">订阅状态</view>\n\t\t\t\t<view class=\"status-content\">\n\t\t\t\t\t<text :class=\"['status-text', subscribed ? 'subscribed' : 'not-subscribed']\">\n\t\t\t\t\t\t{{ subscribed ? '已订阅' : '未订阅' }}\n\t\t\t\t\t</text>\n\t\t\t\t\t<button v-if=\"!subscribed\" @click=\"requestSubscribe\" class=\"subscribe-btn\">\n\t\t\t\t\t\t申请订阅\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 测试功能 -->\n\t\t\t<view class=\"test-card\">\n\t\t\t\t<view class=\"test-title\">测试功能</view>\n\t\t\t\t<view class=\"test-content\">\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<text class=\"input-label\">测试订单号:</text>\n\t\t\t\t\t\t<input v-model=\"testOrderNo\" placeholder=\"请输入订单号\" class=\"input-field\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<button @click=\"sendTestNotification\" :disabled=\"!testOrderNo\" class=\"test-btn\">\n\t\t\t\t\t\t发送测试通知\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 消息历史 -->\n\t\t\t<view class=\"history-card\">\n\t\t\t\t<view class=\"history-title\">消息历史</view>\n\t\t\t\t<view class=\"history-content\">\n\t\t\t\t\t<button @click=\"loadMessageHistory\" class=\"history-btn\">刷新历史</button>\n\t\t\t\t\t<view v-if=\"messageHistory.length > 0\" class=\"history-list\">\n\t\t\t\t\t\t<view v-for=\"(message, index) in messageHistory\" :key=\"index\" class=\"history-item\">\n\t\t\t\t\t\t\t<view class=\"message-status\">\n\t\t\t\t\t\t\t\t<text :class=\"['status-dot', message.send_status === 1 ? 'success' : 'failed']\"></text>\n\t\t\t\t\t\t\t\t<text class=\"status-label\">{{ message.send_status === 1 ? '发送成功' : '发送失败' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"message-time\">{{ formatTime(message.send_time) }}</view>\n\t\t\t\t\t\t\t<view v-if=\"message.error_msg\" class=\"error-msg\">{{ message.error_msg }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"no-history\">\n\t\t\t\t\t\t<text>暂无消息历史</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 调试功能 -->\n\t\t\t<view class=\"debug-card\">\n\t\t\t\t<view class=\"debug-title\">调试功能</view>\n\t\t\t\t<view class=\"debug-content\">\n\t\t\t\t\t<button @click=\"clearOpenidCache\" class=\"debug-btn\">清除OpenID缓存</button>\n\t\t\t\t\t<button @click=\"checkSubscriptionStatus\" class=\"debug-btn\">检查订阅状态</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport subscriptionManager from '../../utils/subscriptionManager.js';\n\timport { showSuccess, showError } from '../../utils/helpers.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tsubscribed: false,\n\t\t\t\ttestOrderNo: '',\n\t\t\t\tmessageHistory: []\n\t\t\t}\n\t\t},\n\t\t\n\t\tonLoad() {\n\t\t\tthis.checkSubscriptionStatus();\n\t\t\tthis.loadMessageHistory();\n\t\t},\n\t\t\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 申请订阅消息\n\t\t\t */\n\t\t\tasync requestSubscribe() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '申请中...' });\n\t\t\t\t\tconst success = await subscriptionManager.requestSubscribeMessage();\n\t\t\t\t\tif (success) {\n\t\t\t\t\t\tthis.subscribed = true;\n\t\t\t\t\t\tshowSuccess('订阅成功');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('申请订阅失败:', error);\n\t\t\t\t\tshowError('申请订阅失败');\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 检查订阅状态\n\t\t\t */\n\t\t\tasync checkSubscriptionStatus() {\n\t\t\t\ttry {\n\t\t\t\t\tthis.subscribed = await subscriptionManager.checkSubscriptionStatus();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('检查订阅状态失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 发送测试通知\n\t\t\t */\n\t\t\tasync sendTestNotification() {\n\t\t\t\tif (!this.testOrderNo.trim()) {\n\t\t\t\t\tshowError('请输入订单号');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '发送中...' });\n\t\t\t\t\tconst success = await subscriptionManager.sendTestNotification(this.testOrderNo.trim());\n\t\t\t\t\tif (success) {\n\t\t\t\t\t\t// 刷新消息历史\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.loadMessageHistory();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('发送测试通知失败:', error);\n\t\t\t\t\tshowError('发送失败');\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 加载消息历史\n\t\t\t */\n\t\t\tasync loadMessageHistory() {\n\t\t\t\ttry {\n\t\t\t\t\tconst result = await subscriptionManager.getMessageHistory(1, 10);\n\t\t\t\t\tif (result) {\n\t\t\t\t\t\tthis.messageHistory = result.messages || [];\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载消息历史失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 清除OpenID缓存\n\t\t\t */\n\t\t\tclearOpenidCache() {\n\t\t\t\tsubscriptionManager.clearOpenidCache();\n\t\t\t\tshowSuccess('OpenID缓存已清除');\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 格式化时间\n\t\t\t */\n\t\t\tformatTime(timeStr) {\n\t\t\t\tif (!timeStr) return '';\n\t\t\t\tconst date = new Date(timeStr);\n\t\t\t\treturn date.toLocaleString('zh-CN');\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.container {\n\t\tpadding: 20rpx;\n\t\tbackground-color: #f5f5f5;\n\t\tmin-height: 100vh;\n\t}\n\t\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.status-card, .test-card, .history-card, .debug-card {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\n\t}\n\t\n\t.status-title, .test-title, .history-title, .debug-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.status-content {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\t\n\t.status-text {\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.subscribed {\n\t\tcolor: #52c41a;\n\t}\n\t\n\t.not-subscribed {\n\t\tcolor: #ff4d4f;\n\t}\n\t\n\t.subscribe-btn, .test-btn, .history-btn, .debug-btn {\n\t\tbackground: #667eea;\n\t\tcolor: white;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 16rpx 32rpx;\n\t\tfont-size: 28rpx;\n\t\tmargin: 10rpx;\n\t}\n\t\n\t.subscribe-btn:active, .test-btn:active, .history-btn:active, .debug-btn:active {\n\t\tbackground: #5a6fd8;\n\t}\n\t\n\t.input-group {\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.input-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 10rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.input-field {\n\t\twidth: 100%;\n\t\tpadding: 20rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.history-list {\n\t\tmargin-top: 20rpx;\n\t}\n\t\n\t.history-item {\n\t\tpadding: 20rpx;\n\t\tborder-bottom: 2rpx solid #f0f0f0;\n\t}\n\t\n\t.message-status {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.status-dot {\n\t\twidth: 16rpx;\n\t\theight: 16rpx;\n\t\tborder-radius: 50%;\n\t\tmargin-right: 16rpx;\n\t}\n\t\n\t.status-dot.success {\n\t\tbackground: #52c41a;\n\t}\n\t\n\t.status-dot.failed {\n\t\tbackground: #ff4d4f;\n\t}\n\t\n\t.status-label {\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t}\n\t\n\t.message-time {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.error-msg {\n\t\tfont-size: 24rpx;\n\t\tcolor: #ff4d4f;\n\t}\n\t\n\t.no-history {\n\t\ttext-align: center;\n\t\tcolor: #999;\n\t\tpadding: 40rpx;\n\t}\n\t\n\t.debug-content {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t}\n</style>\n", "import MiniProgramPage from 'D:/Desktop/Warehouse/front/pages/subscriptionTest/subscriptionTest.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "subscriptionManager", "showSuccess", "showError"], "mappings": ";;;;AAuEC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,gBAAgB,CAAC;AAAA,IAClB;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,wBAAuB;AAC5B,SAAK,mBAAkB;AAAA,EACvB;AAAA,EAED,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,MAAM,mBAAmB;AACxB,UAAI;AACHA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,cAAM,UAAU,MAAMC,8CAAoB;AAC1C,YAAI,SAAS;AACZ,eAAK,aAAa;AAClBC,wBAAW,YAAC,MAAM;AAAA,QACnB;AAAA,MACC,SAAO,OAAO;AACfF,gGAAc,WAAW,KAAK;AAC9BG,sBAAS,UAAC,QAAQ;AAAA,MACnB,UAAU;AACTH,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,0BAA0B;AAC/B,UAAI;AACH,aAAK,aAAa,MAAMC,0BAAmB,oBAAC,wBAAuB;AAAA,MAClE,SAAO,OAAO;AACfD,sBAAA,MAAA,MAAA,SAAA,sDAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,uBAAuB;AAC5B,UAAI,CAAC,KAAK,YAAY,QAAQ;AAC7BG,sBAAS,UAAC,QAAQ;AAClB;AAAA,MACD;AAEA,UAAI;AACHH,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,cAAM,UAAU,MAAMC,8CAAoB,qBAAqB,KAAK,YAAY,KAAI,CAAE;AACtF,YAAI,SAAS;AAEZ,qBAAW,MAAM;AAChB,iBAAK,mBAAkB;AAAA,UACvB,GAAE,GAAI;AAAA,QACR;AAAA,MACC,SAAO,OAAO;AACfD,sBAAA,MAAA,MAAA,SAAA,sDAAc,aAAa,KAAK;AAChCG,sBAAS,UAAC,MAAM;AAAA,MACjB,UAAU;AACTH,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,qBAAqB;AAC1B,UAAI;AACH,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,kBAAkB,GAAG,EAAE;AAChE,YAAI,QAAQ;AACX,eAAK,iBAAiB,OAAO,YAAY,CAAA;AAAA,QAC1C;AAAA,MACC,SAAO,OAAO;AACfD,sBAAA,MAAA,MAAA,SAAA,sDAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,mBAAmB;AAClBC,gCAAmB,oBAAC,iBAAgB;AACpCC,oBAAW,YAAC,aAAa;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW,SAAS;AACnB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,aAAO,KAAK,eAAe,OAAO;AAAA,IACnC;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5KD,GAAG,WAAW,eAAe;"}