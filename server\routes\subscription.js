/**
 * 订阅消息相关API路由
 */

const express = require('express');
const router = express.Router();
const WechatService = require('../utils/wechatService');
const SubscriptionService = require('../utils/subscriptionService');
const { verifyToken } = require('./auth');

const wechatService = new WechatService();
const subscriptionService = new SubscriptionService();

/**
 * 获取用户openid
 * POST /api/subscription/get-openid
 */
router.post('/get-openid', async (req, res) => {
    try {
        const { code } = req.body;
        
        if (!code) {
            return res.status(400).json({
                success: false,
                message: '微信登录code不能为空'
            });
        }

        const openid = await wechatService.getOpenidByCode(code);
        
        res.json({
            success: true,
            message: '获取openid成功',
            data: { openid }
        });

    } catch (error) {
        console.error('❌ 获取openid失败:', error);
        res.status(500).json({
            success: false,
            message: '获取openid失败: ' + error.message
        });
    }
});

/**
 * 保存用户订阅状态
 * POST /api/subscription/save
 */
router.post('/save', verifyToken, async (req, res) => {
    try {
        const { subscribeResult, openid } = req.body;
        const userId = req.user.id;
        const username = req.user.username;

        if (!subscribeResult || !openid) {
            return res.status(400).json({
                success: false,
                message: '订阅结果和openid不能为空'
            });
        }

        // 获取用户工厂信息
        const { query } = require('../config/database');
        const userSql = `
            SELECT factory_name 
            FROM Factory_Login 
            WHERE id = ? AND status = 1
        `;
        
        const users = await query(userSql, [userId]);
        
        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const userInfo = {
            username: username,
            factory_name: users[0].factory_name
        };

        // 保存订阅状态
        for (const [templateId, status] of Object.entries(subscribeResult)) {
            if (status === 'accept') {
                await subscriptionService.saveUserSubscription(openid, templateId, userInfo);
            }
        }
        
        res.json({
            success: true,
            message: '订阅状态保存成功'
        });

    } catch (error) {
        console.error('❌ 保存订阅状态失败:', error);
        res.status(500).json({
            success: false,
            message: '保存订阅状态失败: ' + error.message
        });
    }
});

/**
 * 手动发送订阅消息（测试用）
 * POST /api/subscription/send-notification
 */
router.post('/send-notification', verifyToken, async (req, res) => {
    try {
        const { orderNo } = req.body;
        const username = req.user.username;
        
        if (!orderNo) {
            return res.status(400).json({
                success: false,
                message: '订单号不能为空'
            });
        }

        const result = await subscriptionService.sendOrderImageUploadNotification(orderNo, username);
        
        if (result.success) {
            res.json({
                success: true,
                message: result.message
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message,
                error: result.error
            });
        }

    } catch (error) {
        console.error('❌ 发送订阅消息失败:', error);
        res.status(500).json({
            success: false,
            message: '发送订阅消息失败: ' + error.message
        });
    }
});

/**
 * 获取用户订阅状态
 * GET /api/subscription/status
 */
router.get('/status', verifyToken, async (req, res) => {
    try {
        const username = req.user.username;
        const templateId = wechatService.templateId;
        
        const subscription = await subscriptionService.getUserSubscription(username, templateId);
        
        res.json({
            success: true,
            message: '获取订阅状态成功',
            data: {
                subscribed: !!subscription,
                subscription: subscription
            }
        });

    } catch (error) {
        console.error('❌ 获取订阅状态失败:', error);
        res.status(500).json({
            success: false,
            message: '获取订阅状态失败: ' + error.message
        });
    }
});

/**
 * 获取消息发送历史
 * GET /api/subscription/message-history
 */
router.get('/message-history', verifyToken, async (req, res) => {
    try {
        const { page = 1, limit = 20 } = req.query;
        const username = req.user.username;
        
        // 获取用户的openid
        const subscription = await subscriptionService.getUserSubscription(username, wechatService.templateId);
        
        if (!subscription) {
            return res.json({
                success: true,
                message: '用户未订阅消息',
                data: {
                    messages: [],
                    total: 0,
                    page: parseInt(page),
                    limit: parseInt(limit)
                }
            });
        }

        const { query } = require('../config/database');
        const offset = (page - 1) * limit;
        
        // 查询消息历史
        const messagesSql = `
            SELECT template_id, send_status, error_msg, send_time
            FROM message_logs
            WHERE openid = ?
            ORDER BY send_time DESC
            LIMIT ? OFFSET ?
        `;
        
        const countSql = `
            SELECT COUNT(*) as total
            FROM message_logs
            WHERE openid = ?
        `;
        
        const [messages, countResult] = await Promise.all([
            query(messagesSql, [subscription.openid, parseInt(limit), offset]),
            query(countSql, [subscription.openid])
        ]);
        
        res.json({
            success: true,
            message: '获取消息历史成功',
            data: {
                messages: messages,
                total: countResult[0].total,
                page: parseInt(page),
                limit: parseInt(limit)
            }
        });

    } catch (error) {
        console.error('❌ 获取消息历史失败:', error);
        res.status(500).json({
            success: false,
            message: '获取消息历史失败: ' + error.message
        });
    }
});

module.exports = router;
