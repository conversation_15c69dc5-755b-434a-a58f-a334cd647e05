{"version": 3, "file": "urlConfig.js", "sources": ["utils/urlConfig.js"], "sourcesContent": ["/**\n * URL配置工具\n * 统一管理API基础URL，避免重复代码\n */\n\n/**\n * 获取API基础URL\n * 根据不同环境自动选择合适的URL\n * @returns {string} API基础URL\n */\nexport function getApiBaseUrl() {\n\t// 检查是否在微信小程序环境\n\t// #ifdef MP-WEIXIN\n\treturn 'https://www.mls2005.top';\n\t// #endif\n\t\n\t// 检查是否在H5环境\n\t// #ifdef H5\n\tif (typeof window !== 'undefined' && window.location.protocol === 'https:') {\n\t\treturn 'https://www.mls2005.top';\n\t}\n\treturn 'http://localhost:3001';\n\t// #endif\n\t\n\t// 其他环境默认使用HTTPS\n\treturn 'https://www.mls2005.top';\n}\n\n/**\n * 获取完整的API URL\n * @param {string} path API路径，如 '/api/auth/login'\n * @returns {string} 完整的API URL\n */\nexport function getApiUrl(path) {\n\tconst baseUrl = getApiBaseUrl();\n\t// 确保路径以 / 开头\n\tconst normalizedPath = path.startsWith('/') ? path : `/${path}`;\n\treturn `${baseUrl}${normalizedPath}`;\n}\n\n/**\n * 获取图片URL\n * @param {string} imagePath 图片路径\n * @param {string|number} imageId 图片ID（可选）\n * @returns {string} 图片URL\n */\nexport function getImageUrl(imagePath, imageId = null) {\n\t// 如果是完整的HTTP/HTTPS URL，直接返回\n\tif (imagePath && (imagePath.startsWith('http://') || imagePath.startsWith('https://'))) {\n\t\treturn imagePath;\n\t}\n\n\tconst baseUrl = getApiBaseUrl();\n\n\t// 优先使用imageId通过API访问\n\tif (imageId) {\n\t\treturn `${baseUrl}/api/images/file/${imageId}`;\n\t}\n\n\t// 对于本地文件路径（如微信小程序临时文件），直接返回\n\tif (imagePath && (imagePath.startsWith('file://') || imagePath.startsWith('/') || imagePath.includes('tmp'))) {\n\t\treturn imagePath;\n\t}\n\n\treturn imagePath || '';\n}\n\n/**\n * 获取上传URL\n * @returns {string} 上传API URL\n */\nexport function getUploadUrl() {\n\treturn getApiUrl('/api/images/upload');\n}\n\n/**\n * 获取历史图片URL\n * @param {string} imagePath 图片路径\n * @returns {string} 历史图片URL\n */\nexport function getHistoryImageUrl(imagePath) {\n\tif (!imagePath) return '';\n\n\t// 处理不同类型的路径\n\tlet processedPath = imagePath;\n\n\t// 如果是完整的Windows路径，转换为相对路径\n\tif (imagePath.startsWith('C:\\\\MLS\\\\Warehouse_Img\\\\')) {\n\t\tprocessedPath = imagePath.replace('C:\\\\MLS\\\\Warehouse_Img\\\\', '');\n\t} else if (imagePath.startsWith('C:/MLS/Warehouse_Img/')) {\n\t\tprocessedPath = imagePath.replace('C:/MLS/Warehouse_Img/', '');\n\t} else if (imagePath.startsWith('C:\\\\Warehouse_Img\\\\')) {\n\t\tprocessedPath = imagePath.replace('C:\\\\Warehouse_Img\\\\', '');\n\t} else if (imagePath.startsWith('C:/Warehouse_Img/')) {\n\t\tprocessedPath = imagePath.replace('C:/Warehouse_Img/', '');\n\t}\n\n\t// 将Windows路径分隔符转换为URL路径分隔符\n\tconst urlPath = processedPath.replace(/\\\\/g, '/');\n\n\t// 对路径进行URL编码，但保留路径分隔符\n\tconst encodedPath = urlPath.split('/').map(segment => encodeURIComponent(segment)).join('/');\n\n\treturn getApiUrl(`/api/history/image/${encodedPath}`);\n}\n\n/**\n * 环境检测工具\n */\nexport const ENV = {\n\t// 是否为微信小程序环境\n\tisWeixin: () => {\n\t\t// #ifdef MP-WEIXIN\n\t\treturn true;\n\t\t// #endif\n\t\treturn false;\n\t},\n\t\n\t// 是否为H5环境\n\tisH5: () => {\n\t\t// #ifdef H5\n\t\treturn true;\n\t\t// #endif\n\t\treturn false;\n\t},\n\t\n\t// 是否为HTTPS环境\n\tisHttps: () => {\n\t\t// #ifdef H5\n\t\treturn typeof window !== 'undefined' && window.location.protocol === 'https:';\n\t\t// #endif\n\t\t// #ifdef MP-WEIXIN\n\t\treturn true; // 小程序默认使用HTTPS\n\t\t// #endif\n\t\treturn false;\n\t},\n\t\n\t// 是否为生产环境\n\tisProduction: () => {\n\t\treturn ENV.isWeixin() || ENV.isHttps();\n\t}\n};\n\nexport default {\n\tgetApiBaseUrl,\n\tgetApiUrl,\n\tgetImageUrl,\n\tgetUploadUrl,\n\tgetHistoryImageUrl,\n\tENV\n};\n"], "names": [], "mappings": ";AAUO,SAAS,gBAAgB;AAG/B,SAAO;AAaR;AAOO,SAAS,UAAU,MAAM;AAC/B,QAAM,UAAU;AAEhB,QAAM,iBAAiB,KAAK,WAAW,GAAG,IAAI,OAAO,IAAI,IAAI;AAC7D,SAAO,GAAG,OAAO,GAAG,cAAc;AACnC;AAQO,SAAS,YAAY,WAAW,UAAU,MAAM;AAEtD,MAAI,cAAc,UAAU,WAAW,SAAS,KAAK,UAAU,WAAW,UAAU,IAAI;AACvF,WAAO;AAAA,EACP;AAED,QAAM,UAAU;AAGhB,MAAI,SAAS;AACZ,WAAO,GAAG,OAAO,oBAAoB,OAAO;AAAA,EAC5C;AAGD,MAAI,cAAc,UAAU,WAAW,SAAS,KAAK,UAAU,WAAW,GAAG,KAAK,UAAU,SAAS,KAAK,IAAI;AAC7G,WAAO;AAAA,EACP;AAED,SAAO,aAAa;AACrB;AAMO,SAAS,eAAe;AAC9B,SAAO,UAAU,oBAAoB;AACtC;AAOO,SAAS,mBAAmB,WAAW;AAC7C,MAAI,CAAC;AAAW,WAAO;AAGvB,MAAI,gBAAgB;AAGpB,MAAI,UAAU,WAAW,0BAA0B,GAAG;AACrD,oBAAgB,UAAU,QAAQ,4BAA4B,EAAE;AAAA,EAChE,WAAU,UAAU,WAAW,uBAAuB,GAAG;AACzD,oBAAgB,UAAU,QAAQ,yBAAyB,EAAE;AAAA,EAC7D,WAAU,UAAU,WAAW,qBAAqB,GAAG;AACvD,oBAAgB,UAAU,QAAQ,uBAAuB,EAAE;AAAA,EAC3D,WAAU,UAAU,WAAW,mBAAmB,GAAG;AACrD,oBAAgB,UAAU,QAAQ,qBAAqB,EAAE;AAAA,EACzD;AAGD,QAAM,UAAU,cAAc,QAAQ,OAAO,GAAG;AAGhD,QAAM,cAAc,QAAQ,MAAM,GAAG,EAAE,IAAI,aAAW,mBAAmB,OAAO,CAAC,EAAE,KAAK,GAAG;AAE3F,SAAO,UAAU,sBAAsB,WAAW,EAAE;AACrD;AAKO,MAAM,MAAM;AAAA;AAAA,EAElB,UAAU,MAAM;AAEf,WAAO;AAAA,EAGP;AAAA;AAAA,EAGD,MAAM,MAAM;AAIX,WAAO;AAAA,EACP;AAAA;AAAA,EAGD,SAAS,MAAM;AAKd,WAAO;AAAA,EAGP;AAAA;AAAA,EAGD,cAAc,MAAM;AACnB,WAAO,IAAI,SAAQ,KAAM,IAAI,QAAO;AAAA,EACpC;AACF;AAEA,MAAe,YAAA;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;"}