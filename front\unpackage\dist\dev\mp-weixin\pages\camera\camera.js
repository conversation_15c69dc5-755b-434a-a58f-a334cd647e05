"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_userManager = require("../../utils/userManager.js");
const utils_urlConfig = require("../../utils/urlConfig.js");
const _sfc_main = {
  data() {
    return {
      orderNumber: "",
      shippingNumber: "DEFAULT",
      userInfo: {},
      selectedImages: [],
      isUploading: false,
      uploadProgress: {
        current: 0,
        total: 0
      },
      uploadStatusText: ""
    };
  },
  computed: {
    progressPercentage() {
      if (this.uploadProgress.total === 0)
        return 0;
      return Math.round(this.uploadProgress.current / this.uploadProgress.total * 100);
    }
  },
  methods: {
    /**
     * 拍照
     */
    takePhoto() {
      common_vendor.index.chooseImage({
        count: 9 - this.selectedImages.length,
        // 最多9张
        sizeType: ["original", "compressed"],
        sourceType: ["camera"],
        success: (res) => {
          this.addImages(res.tempFilePaths);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/camera/camera.vue:155", "拍照失败:", err);
          utils_helpers.showError("拍照失败");
        }
      });
    },
    /**
     * 从相册选择
     */
    chooseFromAlbum() {
      common_vendor.index.chooseImage({
        count: 9 - this.selectedImages.length,
        // 最多9张
        sizeType: ["original", "compressed"],
        sourceType: ["album"],
        success: (res) => {
          this.addImages(res.tempFilePaths);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/camera/camera.vue:174", "选择图片失败:", err);
          utils_helpers.showError("选择图片失败");
        }
      });
    },
    /**
     * 添加图片到选择列表
     */
    addImages(imagePaths) {
      imagePaths.forEach((path) => {
        if (this.selectedImages.length < 9) {
          let processedPath = path;
          if (path && !path.startsWith("http") && !path.startsWith("file://")) {
            processedPath = path;
          }
          this.selectedImages.push({
            path: processedPath,
            name: `image_${Date.now()}_${Math.random().toString(36).substring(2, 11)}.jpg`
          });
        }
      });
      if (this.selectedImages.length >= 9) {
        utils_helpers.showError("最多只能选择9张图片");
      }
    },
    /**
     * 移除图片
     */
    removeImage(index) {
      this.selectedImages.splice(index, 1);
    },
    /**
     * 获取预览图片的正确路径
     */
    getPreviewImageSrc(imagePath) {
      if (!imagePath)
        return "";
      if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
        return imagePath;
      }
      if (imagePath.startsWith("file://")) {
        return imagePath;
      }
      return imagePath;
    },
    /**
     * 处理上传
     */
    async handleUpload() {
      if (this.selectedImages.length === 0) {
        utils_helpers.showError("请先选择图片");
        return;
      }
      this.isUploading = true;
      this.uploadProgress = {
        current: 0,
        total: this.selectedImages.length
      };
      try {
        for (let i = 0; i < this.selectedImages.length; i++) {
          const image = this.selectedImages[i];
          this.uploadStatusText = `正在上传第 ${i + 1} 张图片...`;
          await this.uploadSingleImage(image);
          this.uploadProgress.current = i + 1;
        }
        utils_helpers.showSuccess(`成功上传 ${this.selectedImages.length} 张图片`);
        common_vendor.index.$emit("imageUploaded", {
          orderNumber: this.orderNumber,
          count: this.selectedImages.length
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/camera/camera.vue:274", "上传失败:", error);
        utils_helpers.showError("上传失败：" + error.message);
      } finally {
        this.isUploading = false;
      }
    },
    /**
     * 上传单张图片
     */
    async uploadSingleImage(image) {
      return new Promise((resolve, reject) => {
        const authHeaders = utils_userManager.userManager.getAuthHeaders();
        common_vendor.index.__f__("log", "at pages/camera/camera.vue:288", "开始上传图片:", {
          url: utils_urlConfig.urlConfig.getUploadUrl(),
          filePath: image.path,
          orderNumber: this.orderNumber,
          factoryName: this.userInfo.factory_name
        });
        common_vendor.index.uploadFile({
          url: utils_urlConfig.urlConfig.getUploadUrl(),
          filePath: image.path,
          name: "file",
          formData: {
            order_number: this.orderNumber,
            factory_name: this.userInfo.factory_name,
            shipping_number: this.shippingNumber
          },
          header: {
            "Authorization": authHeaders.Authorization
          },
          timeout: 6e4,
          // 增加超时时间到60秒
          success: (uploadRes) => {
            common_vendor.index.__f__("log", "at pages/camera/camera.vue:309", "上传响应:", uploadRes);
            try {
              if (uploadRes.statusCode !== 200) {
                common_vendor.index.__f__("error", "at pages/camera/camera.vue:312", "服务器返回错误状态码:", uploadRes.statusCode, uploadRes.data);
                reject(new Error(`服务器错误 (${uploadRes.statusCode}): ${uploadRes.data || "未知错误"}`));
                return;
              }
              const result = JSON.parse(uploadRes.data);
              if (result.success) {
                common_vendor.index.__f__("log", "at pages/camera/camera.vue:319", "上传成功:", result);
                resolve(result);
              } else {
                common_vendor.index.__f__("error", "at pages/camera/camera.vue:322", "上传失败:", result.message);
                reject(new Error(result.message || "上传失败"));
              }
            } catch (parseError) {
              common_vendor.index.__f__("error", "at pages/camera/camera.vue:326", "解析响应失败:", parseError, uploadRes.data);
              reject(new Error("服务器响应格式错误"));
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/camera/camera.vue:331", "上传请求失败:", err);
            let errorMessage = "网络连接失败";
            if (err.errMsg) {
              if (err.errMsg.includes("timeout")) {
                errorMessage = "上传超时，请检查网络连接";
              } else if (err.errMsg.includes("fail")) {
                errorMessage = "上传失败，请重试";
              } else {
                errorMessage = err.errMsg;
              }
            }
            reject(new Error(errorMessage));
          }
        });
      });
    },
    /**
     * 初始化
     */
    init() {
      if (utils_userManager.userManager.requireLogin()) {
        this.userInfo = utils_userManager.userManager.getUserInfo();
      }
    }
  },
  onLoad(options) {
    this.orderNumber = options.orderNumber || "";
    this.shippingNumber = options.shippingNumber || "DEFAULT";
    if (!this.orderNumber) {
      utils_helpers.showError("订单号不能为空");
      common_vendor.index.navigateBack();
      return;
    }
    common_vendor.index.__f__("log", "at pages/camera/camera.vue:369", "📦 接收到发货单号:", this.shippingNumber);
    this.init();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.orderNumber),
    b: common_vendor.o((...args) => $options.takePhoto && $options.takePhoto(...args)),
    c: common_vendor.o((...args) => $options.chooseFromAlbum && $options.chooseFromAlbum(...args)),
    d: $data.selectedImages.length > 0
  }, $data.selectedImages.length > 0 ? {
    e: common_vendor.t($data.selectedImages.length),
    f: common_vendor.f($data.selectedImages, (image, index, i0) => {
      return {
        a: $options.getPreviewImageSrc(image.path),
        b: common_vendor.o(($event) => $options.removeImage(index), index),
        c: index
      };
    })
  } : {}, {
    g: $data.selectedImages.length > 0
  }, $data.selectedImages.length > 0 ? common_vendor.e({
    h: $data.isUploading
  }, $data.isUploading ? {
    i: common_vendor.t($data.uploadProgress.current),
    j: common_vendor.t($data.uploadProgress.total)
  } : {
    k: common_vendor.t($data.selectedImages.length)
  }, {
    l: $data.isUploading ? 1 : "",
    m: $data.isUploading,
    n: common_vendor.o((...args) => $options.handleUpload && $options.handleUpload(...args))
  }) : {}, {
    o: $data.isUploading
  }, $data.isUploading ? {
    p: $options.progressPercentage + "%",
    q: common_vendor.t($data.uploadProgress.current),
    r: common_vendor.t($data.uploadProgress.total),
    s: common_vendor.t($options.progressPercentage),
    t: common_vendor.t($data.uploadStatusText)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7b8d50ad"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/camera/camera.js.map
