<view class="container data-v-0aab7bd3"><view class="header data-v-0aab7bd3"><text class="title data-v-0aab7bd3">通知管理</text><button bindtap="{{a}}" class="refresh-btn data-v-0aab7bd3">刷新</button></view><view class="stats-section data-v-0aab7bd3"><view class="stat-card data-v-0aab7bd3"><text class="stat-number data-v-0aab7bd3">{{b}}</text><text class="stat-label data-v-0aab7bd3">总通知</text></view><view class="stat-card data-v-0aab7bd3"><text class="stat-number data-v-0aab7bd3">{{c}}</text><text class="stat-label data-v-0aab7bd3">待处理</text></view><view class="stat-card data-v-0aab7bd3"><text class="stat-number data-v-0aab7bd3">{{d}}</text><text class="stat-label data-v-0aab7bd3">已发送</text></view><view class="stat-card data-v-0aab7bd3"><text class="stat-number data-v-0aab7bd3">{{e}}</text><text class="stat-label data-v-0aab7bd3">失败</text></view></view><view class="filter-section data-v-0aab7bd3"><picker class="data-v-0aab7bd3" bindchange="{{g}}" value="{{h}}" range="{{i}}"><view class="picker-item data-v-0aab7bd3"> 状态: {{f}}</view></picker><picker class="data-v-0aab7bd3" bindchange="{{k}}" value="{{l}}" range="{{m}}"><view class="picker-item data-v-0aab7bd3"> 类型: {{j}}</view></picker></view><view class="notification-list data-v-0aab7bd3"><view wx:for="{{n}}" wx:for-item="notification" wx:key="q" class="notification-item data-v-0aab7bd3"><view class="notification-header data-v-0aab7bd3"><text class="order-no data-v-0aab7bd3">{{notification.a}}</text><view class="{{['data-v-0aab7bd3', 'status-badge', notification.c]}}">{{notification.b}}</view></view><view class="notification-content data-v-0aab7bd3"><text class="receiver data-v-0aab7bd3">客户: {{notification.d}}</text><text class="follower data-v-0aab7bd3">跟单: {{notification.e}}</text><text class="type data-v-0aab7bd3">类型: {{notification.f}}</text><text class="time data-v-0aab7bd3">创建: {{notification.g}}</text><view wx:if="{{notification.h}}" class="processed-time data-v-0aab7bd3"><text class="data-v-0aab7bd3">处理: {{notification.i}}</text></view><view wx:if="{{notification.j}}" class="retry-info data-v-0aab7bd3"><text class="data-v-0aab7bd3">重试次数: {{notification.k}}</text></view><view wx:if="{{notification.l}}" class="error-message data-v-0aab7bd3"><text class="data-v-0aab7bd3">错误: {{notification.m}}</text></view></view><view class="notification-actions data-v-0aab7bd3"><button wx:if="{{notification.n}}" bindtap="{{notification.o}}" class="action-btn retry-btn data-v-0aab7bd3"> 重试 </button><button bindtap="{{notification.p}}" class="action-btn delete-btn data-v-0aab7bd3"> 删除 </button></view></view><view wx:if="{{o}}" class="empty-state data-v-0aab7bd3"><text class="data-v-0aab7bd3">暂无通知记录</text></view></view><view wx:if="{{p}}" class="pagination data-v-0aab7bd3"><button bindtap="{{q}}" disabled="{{r}}" class="page-btn data-v-0aab7bd3">上一页</button><text class="page-info data-v-0aab7bd3">{{s}} / {{t}}</text><button bindtap="{{v}}" disabled="{{w}}" class="page-btn data-v-0aab7bd3">下一页</button></view><view class="action-section data-v-0aab7bd3"><button bindtap="{{x}}" class="test-btn data-v-0aab7bd3">测试通知</button><button bindtap="{{y}}" class="cleanup-btn data-v-0aab7bd3">清理已发送</button></view><view wx:if="{{z}}" class="modal-overlay data-v-0aab7bd3" bindtap="{{K}}"><view class="modal-content data-v-0aab7bd3" catchtap="{{J}}"><view class="modal-header data-v-0aab7bd3"><text class="modal-title data-v-0aab7bd3">测试订单生成通知</text><text bindtap="{{A}}" class="modal-close data-v-0aab7bd3">×</text></view><view class="modal-body data-v-0aab7bd3"><view class="input-group data-v-0aab7bd3"><text class="input-label data-v-0aab7bd3">订单号:</text><input placeholder="请输入测试订单号" class="input-field data-v-0aab7bd3" value="{{B}}" bindinput="{{C}}"/></view><view class="input-group data-v-0aab7bd3"><text class="input-label data-v-0aab7bd3">接收方:</text><input placeholder="请输入工厂名称" class="input-field data-v-0aab7bd3" value="{{D}}" bindinput="{{E}}"/></view><view class="input-group data-v-0aab7bd3"><text class="input-label data-v-0aab7bd3">跟单员:</text><input placeholder="请输入跟单员" class="input-field data-v-0aab7bd3" value="{{F}}" bindinput="{{G}}"/></view></view><view class="modal-footer data-v-0aab7bd3"><button bindtap="{{H}}" class="cancel-btn data-v-0aab7bd3">取消</button><button bindtap="{{I}}" class="confirm-btn data-v-0aab7bd3">发送</button></view></view></view></view>