"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_userManager = require("../../utils/userManager.js");
const utils_urlConfig = require("../../utils/urlConfig.js");
const _sfc_main = {
  data() {
    return {
      userInfo: {},
      newOrderNumber: "",
      orderList: [],
      isLoading: false,
      isLoadingOrders: false,
      isRefreshing: false,
      // 查询相关
      showQueryResult: false,
      isQuerying: false,
      queryResult: null
    };
  },
  methods: {
    /**
     * 处理查询订单（基于shipping_detail表，支持模糊查询）
     */
    async handleQueryOrder() {
      if (!this.newOrderNumber.trim()) {
        utils_helpers.showError("请输入订单号或关键词");
        return;
      }
      this.isQuerying = true;
      this.showQueryResult = true;
      this.queryResult = null;
      try {
        const searchTerm = this.newOrderNumber.trim();
        common_vendor.index.__f__("log", "at pages/order/order.vue:239", "开始模糊查询订单:", searchTerm);
        const response = await this.queryOrderRequest(searchTerm);
        if (response.success && response.data) {
          this.queryResult = response.data;
          if (Array.isArray(response.data)) {
            common_vendor.index.__f__("log", "at pages/order/order.vue:247", `模糊查询成功: 找到 ${response.data.length} 个匹配的订单`);
            common_vendor.index.showToast({
              title: `找到 ${response.data.length} 个匹配订单`,
              icon: "success",
              duration: 2e3
            });
          } else {
            common_vendor.index.__f__("log", "at pages/order/order.vue:254", "精确查询成功:", this.queryResult);
            common_vendor.index.showToast({
              title: "查询成功",
              icon: "success",
              duration: 1500
            });
          }
        } else {
          this.queryResult = null;
          common_vendor.index.__f__("log", "at pages/order/order.vue:263", "未找到匹配的订单:", searchTerm);
          common_vendor.index.showToast({
            title: "未找到匹配的订单",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/order.vue:271", "查询订单失败:", error);
        this.queryResult = null;
        utils_helpers.showError("查询失败: " + error.message);
      } finally {
        this.isQuerying = false;
      }
    },
    /**
     * 关闭查询结果
     */
    closeQueryResult() {
      this.showQueryResult = false;
      this.queryResult = null;
      this.newOrderNumber = "";
    },
    /**
     * 跳转到图片管理页面
     */
    goToImageManage(orderNumber) {
      common_vendor.index.navigateTo({
        url: `/pages/imageManage/imageManage?orderNumber=${encodeURIComponent(orderNumber)}`,
        success: () => {
          common_vendor.index.__f__("log", "at pages/order/order.vue:295", "跳转到图片管理页面:", orderNumber);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/order/order.vue:298", "跳转失败:", err);
          utils_helpers.showError("页面跳转失败");
        }
      });
    },
    /**
     * 加载历史订单
     */
    async loadOrderHistory() {
      if (!this.userInfo.factory_name) {
        common_vendor.index.__f__("log", "at pages/order/order.vue:309", "用户工厂名称为空，跳过加载订单历史");
        return;
      }
      this.isLoadingOrders = true;
      try {
        common_vendor.index.__f__("log", "at pages/order/order.vue:316", "开始加载订单历史...");
        const response = await this.getOrderHistoryRequest();
        if (response.success) {
          this.orderList = response.data || [];
          common_vendor.index.__f__("log", "at pages/order/order.vue:321", "订单历史加载成功:", this.orderList.length, "个订单");
        } else {
          common_vendor.index.__f__("error", "at pages/order/order.vue:323", "获取订单历史失败:", response.message);
          this.orderList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/order.vue:327", "加载订单历史失败:", error);
        this.orderList = [];
        utils_helpers.showError("加载订单历史失败");
      } finally {
        this.isLoadingOrders = false;
      }
    },
    /**
     * 获取订单历史请求
     */
    async getOrderHistoryRequest() {
      return new Promise((resolve, reject) => {
        const requestConfig = utils_userManager.userManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl("/api/orders/history"),
          method: "GET",
          timeout: 1e4,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`服务器错误 (${res.statusCode})`));
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/order/order.vue:352", "获取订单历史请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
        common_vendor.index.request(requestConfig);
      });
    },
    /**
     * 查询订单请求
     */
    async queryOrderRequest(orderNumber) {
      return new Promise((resolve, reject) => {
        const requestConfig = utils_userManager.userManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl(`/api/orders/query/${encodeURIComponent(orderNumber)}`),
          method: "GET",
          timeout: 1e4,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else if (res.statusCode === 404) {
              resolve({ success: false, message: "订单不存在" });
            } else {
              reject(new Error(`服务器错误 (${res.statusCode})`));
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/order/order.vue:381", "查询订单请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
        common_vendor.index.request(requestConfig);
      });
    },
    /**
     * 下拉刷新
     */
    async handleRefresh() {
      common_vendor.index.__f__("log", "at pages/order/order.vue:394", "开始下拉刷新...");
      this.isRefreshing = true;
      try {
        await this.loadOrderHistory();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/order.vue:399", "下拉刷新失败:", error);
      } finally {
        this.isRefreshing = false;
        common_vendor.index.__f__("log", "at pages/order/order.vue:402", "下拉刷新完成");
      }
    },
    /**
     * 处理退出登录
     */
    handleLogout() {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            utils_userManager.userManager.logout();
            common_vendor.index.reLaunch({
              url: "/pages/home/<USER>"
            });
          }
        }
      });
    },
    /**
     * 格式化时间
     */
    formatTime(timeStr) {
      if (!timeStr)
        return "未知";
      try {
        const date = new Date(timeStr);
        const now = /* @__PURE__ */ new Date();
        const diff = now - date;
        if (diff < 6e4) {
          return "刚刚";
        } else if (diff < 36e5) {
          return `${Math.floor(diff / 6e4)}分钟前`;
        } else if (diff < 864e5) {
          return `${Math.floor(diff / 36e5)}小时前`;
        } else {
          return date.toLocaleDateString();
        }
      } catch (error) {
        return "未知";
      }
    },
    /**
     * 初始化数据
     */
    initData() {
      this.orderList = [];
      this.isLoadingOrders = false;
      this.isRefreshing = false;
      this.isLoading = false;
      this.newOrderNumber = "";
    },
    /**
     * 初始化用户信息
     */
    initUserInfo() {
      this.initData();
      if (utils_userManager.userManager.requireLogin()) {
        this.userInfo = utils_userManager.userManager.getUserInfo();
        this.$nextTick(() => {
          setTimeout(() => {
            this.loadOrderHistory();
          }, 100);
        });
      }
    }
  },
  onLoad() {
    this.initUserInfo();
  },
  onShow() {
    this.$nextTick(() => {
      if (this.userInfo.factory_name) {
        this.loadOrderHistory();
      }
    });
  },
  onReady() {
    common_vendor.index.__f__("log", "at pages/order/order.vue:500", "订单页面渲染完成");
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.userInfo.factory_name || "未知工厂"),
    b: common_vendor.t($data.userInfo.username),
    c: common_vendor.o((...args) => $options.handleLogout && $options.handleLogout(...args)),
    d: $data.isLoading,
    e: common_vendor.o((...args) => $options.handleQueryOrder && $options.handleQueryOrder(...args)),
    f: $data.newOrderNumber,
    g: common_vendor.o(($event) => $data.newOrderNumber = $event.detail.value),
    h: !$data.newOrderNumber.trim() || $data.isLoading,
    i: common_vendor.o((...args) => $options.handleQueryOrder && $options.handleQueryOrder(...args)),
    j: $data.showQueryResult
  }, $data.showQueryResult ? common_vendor.e({
    k: common_vendor.o((...args) => $options.closeQueryResult && $options.closeQueryResult(...args)),
    l: $data.isQuerying
  }, $data.isQuerying ? {} : common_vendor.e({
    m: $data.queryResult
  }, $data.queryResult ? common_vendor.e({
    n: !Array.isArray($data.queryResult)
  }, !Array.isArray($data.queryResult) ? {
    o: common_vendor.t($data.queryResult.order_number),
    p: common_vendor.t($options.formatTime($data.queryResult.created_date)),
    q: common_vendor.t($options.formatTime($data.queryResult.latest_upload)),
    r: common_vendor.t($data.queryResult.image_count || 0),
    s: common_vendor.o(($event) => $options.goToImageManage($data.queryResult.order_number))
  } : {
    t: common_vendor.t($data.queryResult.length),
    v: common_vendor.f($data.queryResult, (order, index, i0) => {
      return {
        a: common_vendor.t(order.order_number),
        b: common_vendor.t($options.formatTime(order.created_date)),
        c: common_vendor.t($options.formatTime(order.latest_upload)),
        d: common_vendor.t(order.image_count || 0),
        e: index,
        f: common_vendor.o(($event) => $options.goToImageManage(order.order_number), index)
      };
    })
  }) : {})) : {}, {
    w: common_vendor.t($data.orderList.length),
    x: $data.isLoadingOrders
  }, $data.isLoadingOrders ? {} : $data.orderList.length > 0 ? {
    z: common_vendor.f($data.orderList, (order, index, i0) => {
      return {
        a: common_vendor.t(order.order_number),
        b: common_vendor.t($options.formatTime(order.created_date)),
        c: common_vendor.t($options.formatTime(order.latest_upload)),
        d: common_vendor.t(order.image_count || 0),
        e: index,
        f: common_vendor.o(($event) => $options.goToImageManage(order.order_number), index)
      };
    }),
    A: $data.isRefreshing,
    B: common_vendor.o((...args) => $options.handleRefresh && $options.handleRefresh(...args))
  } : {}, {
    y: $data.orderList.length > 0
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-93207a4f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/order.js.map
