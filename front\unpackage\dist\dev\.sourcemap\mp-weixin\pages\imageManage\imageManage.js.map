{"version": 3, "file": "imageManage.js", "sources": ["pages/imageManage/imageManage.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW1hZ2VNYW5hZ2UvaW1hZ2VNYW5hZ2UudnVl"], "sourcesContent": ["<template>\n\t<view class=\"image-manage-container\">\n\t\t<!-- 头部信息 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"order-info\">\n\t\t\t\t<text class=\"order-icon\">📦</text>\n\t\t\t\t<view class=\"order-details\">\n\t\t\t\t\t<text class=\"order-label\">订单号</text>\n\t\t\t\t\t<text class=\"order-number\">{{ orderNumber }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"image-count\">\n\t\t\t\t<text class=\"count-text\">{{ pagination.total || imageList.length }} 张图片</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 当前发货单号显示 -->\n\t\t<view class=\"current-shipping\" v-if=\"shippingNumbers.length === 1\">\n\t\t\t<text class=\"shipping-info\">📦 发货单号: {{ shippingNumbers[0] }}</text>\n\t\t</view>\n\n\t\t<!-- 发货详细信息 -->\n\t\t<view class=\"shipping-details\" v-if=\"shippingDetails.length > 0\">\n\t\t\t<view class=\"details-header\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<text class=\"details-icon\">📋</text>\n\t\t\t\t\t<text class=\"details-title\">发货信息</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"add-info-btn\" @click=\"showAddInfoModal\">\n\t\t\t\t\t<text class=\"add-icon\">➕</text>\n\t\t\t\t\t<text class=\"add-text\">新增信息</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\n\t\t\t<!-- 发货信息卡片 -->\n\t\t\t<view class=\"details-cards\">\n\t\t\t\t<view\n\t\t\t\t\tclass=\"detail-card\"\n\t\t\t\t\tv-for=\"(detail, index) in shippingDetails\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t>\n\t\t\t\t\t<!-- 基本信息行 -->\n\t\t\t\t\t<view class=\"card-row\">\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">发货日期</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ formatDate(detail.notice_date) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">发货单号</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.shipping_no || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 公司信息行 -->\n\t\t\t\t\t<view class=\"card-row\">\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">加工厂</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.deliver_company || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">跟单员</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.follower || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 产品信息行 -->\n\t\t\t\t\t<view class=\"card-row\">\n\t\t\t\t\t\t<view class=\"info-item full-width\">\n\t\t\t\t\t\t\t<text class=\"info-label\">坯布商坯布名</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.product_name || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 规格信息行 -->\n\t\t\t\t\t<view class=\"card-row\">\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">规格</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.spec || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">门幅</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.width || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 重量数量行 -->\n\t\t\t\t\t<view class=\"card-row\">\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">克重</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.weight || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">匹数/件数</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.quantity || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 备注行 -->\n\t\t\t\t\t<view class=\"card-row\" v-if=\"detail.remark\">\n\t\t\t\t\t\t<view class=\"info-item full-width\">\n\t\t\t\t\t\t\t<text class=\"info-label\">备注</text>\n\t\t\t\t\t\t\t<text class=\"info-value remark-text\">{{ detail.remark }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 新增信息行 -->\n\t\t\t\t\t<view class=\"card-row\" v-if=\"detail.length || detail.weight_total || detail.actual_price\">\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">数量(米)</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.length || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<text class=\"info-label\">重量(千克)</text>\n\t\t\t\t\t\t\t<text class=\"info-value\">{{ detail.weight_total || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"card-row\" v-if=\"detail.actual_price\">\n\t\t\t\t\t\t<view class=\"info-item full-width\">\n\t\t\t\t\t\t\t<text class=\"info-label\">价格</text>\n\t\t\t\t\t\t\t<text class=\"info-value price-text\">{{ detail.actual_price || '-' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 发货单号选择 -->\n\t\t<view class=\"shipping-selector\" v-if=\"shippingNumbers.length > 1\">\n\t\t\t<view class=\"selector-title\">\n\t\t\t\t<text class=\"title-icon\">📦</text>\n\t\t\t\t<text class=\"title-text\">发货单号</text>\n\t\t\t</view>\n\t\t\t<view class=\"shipping-tabs\">\n\t\t\t\t<view\n\t\t\t\t\tclass=\"shipping-tab\"\n\t\t\t\t\t:class=\"{ active: currentShippingNumber === 'ALL' }\"\n\t\t\t\t\t@click=\"switchShippingNumber('ALL')\"\n\t\t\t\t>\n\t\t\t\t\t<text class=\"tab-text\">全部</text>\n\t\t\t\t</view>\n\t\t\t\t<view\n\t\t\t\t\tv-for=\"(shippingNo, index) in shippingNumbers\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\tclass=\"shipping-tab\"\n\t\t\t\t\t:class=\"{ active: currentShippingNumber === shippingNo }\"\n\t\t\t\t\t@click=\"switchShippingNumber(shippingNo)\"\n\t\t\t\t>\n\t\t\t\t\t<text class=\"tab-text\">{{ shippingNo }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 操作按钮区域 -->\n\t\t<view class=\"action-section\">\n\t\t\t<button class=\"action-btn add-btn\" @click=\"goToCamera\">\n\t\t\t\t<text class=\"btn-icon\">📷</text>\n\t\t\t\t<text class=\"btn-text\">新增</text>\n\t\t\t</button>\n\t\t\t<button\n\t\t\t\tclass=\"action-btn select-btn\"\n\t\t\t\t:class=\"{ 'active': isSelectMode }\"\n\t\t\t\t@click=\"toggleSelectMode\"\n\t\t\t>\n\t\t\t\t<text class=\"btn-icon\">{{ isSelectMode ? '✓' : '🗑️' }}</text>\n\t\t\t\t<text class=\"btn-text\">{{ isSelectMode ? '取消选择' : '选择删除' }}</text>\n\t\t\t</button>\n\t\t\t<button\n\t\t\t\tclass=\"action-btn delete-btn\"\n\t\t\t\t:class=\"{ 'disabled': selectedImages.length === 0 }\"\n\t\t\t\t:disabled=\"selectedImages.length === 0\"\n\t\t\t\t@click=\"handleDeleteSelected\"\n\t\t\t\tv-if=\"isSelectMode\"\n\t\t\t>\n\t\t\t\t<text class=\"btn-icon\">🗑️</text>\n\t\t\t\t<text class=\"btn-text\">删除 ({{ selectedImages.length }})</text>\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 选择模式提示 -->\n\t\t<view class=\"select-tip\" v-if=\"isSelectMode\">\n\t\t\t<text class=\"tip-text\">💡 点击图片进行选择，长按可以快速选择</text>\n\t\t</view>\n\n\t\t<!-- 图片网格区域 -->\n\t\t<view class=\"image-grid-section\">\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view class=\"loading-section\" v-if=\"isLoading\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<text class=\"loading-text\">加载图片中...</text>\n\t\t\t</view>\n\n\t\t\t<!-- 图片网格 -->\n\t\t\t<view v-else-if=\"hasImages\" class=\"image-grid\">\n\t\t\t\t<view\n\t\t\t\t\tclass=\"image-item\"\n\t\t\t\t\tv-for=\"(image, index) in imageList\"\n\t\t\t\t\t:key=\"image.id\"\n\t\t\t\t\t@click=\"handleImageClick({ image, index })\"\n\t\t\t\t\t@longpress=\"handleImageLongPress(image)\"\n\t\t\t\t>\n\t\t\t\t\t<!-- 选择状态覆盖层 -->\n\t\t\t\t\t<view class=\"select-overlay\" v-if=\"isSelectMode\">\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"select-checkbox\"\n\t\t\t\t\t\t\t:class=\"{ 'selected': selectedImages.includes(image.id) }\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text class=\"checkbox-icon\" v-if=\"selectedImages.includes(image.id)\">✓</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 图片 -->\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"image\"\n\t\t\t\t\t\t:src=\"getImageUrl(image.image_path, image.id)\"\n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t:lazy-load=\"true\"\n\t\t\t\t\t\t@error=\"handleImageError\"\n\t\t\t\t\t/>\n\n\t\t\t\t\t<!-- 图片信息 -->\n\t\t\t\t\t<view class=\"image-info\">\n\t\t\t\t\t\t<text class=\"image-name\">{{ image.image_name }}</text>\n\t\t\t\t\t\t<text class=\"image-time\">{{ formatTime(image.upload_date) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 分页控件 -->\n\t\t\t<view v-if=\"pagination.totalPages > 1\" class=\"pagination-section\">\n\t\t\t\t<view class=\"pagination-info\">\n\t\t\t\t\t<text class=\"page-info\">第 {{ pagination.page }} 页，共 {{ pagination.totalPages }} 页</text>\n\t\t\t\t\t<text class=\"total-info\">总计 {{ pagination.total }} 张图片</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"pagination-controls\">\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"page-btn prev-btn\"\n\t\t\t\t\t\t:disabled=\"!pagination.hasPrev\"\n\t\t\t\t\t\t@click=\"goToPrevPage\"\n\t\t\t\t\t>\n\t\t\t\t\t\t上一页\n\t\t\t\t\t</button>\n\t\t\t\t\t<view class=\"page-numbers\">\n\t\t\t\t\t\t<text\n\t\t\t\t\t\t\tclass=\"page-number\"\n\t\t\t\t\t\t\t:class=\"{ 'current': pagination.page === pageNum }\"\n\t\t\t\t\t\t\tv-for=\"pageNum in getPageNumbers()\"\n\t\t\t\t\t\t\t:key=\"pageNum\"\n\t\t\t\t\t\t\t@click=\"goToPage(pageNum)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{{ pageNum }}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"page-btn next-btn\"\n\t\t\t\t\t\t:disabled=\"!pagination.hasNext\"\n\t\t\t\t\t\t@click=\"goToNextPage\"\n\t\t\t\t\t>\n\t\t\t\t\t\t下一页\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 空状态 -->\n\t\t\t<view class=\"empty-state\" v-else-if=\"shouldShowEmpty\">\n\t\t\t\t<text class=\"empty-icon\">📷</text>\n\t\t\t\t<text class=\"empty-text\">暂无图片</text>\n\t\t\t\t<text class=\"empty-hint\">点击\"新增\"按钮上传图片</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 新增信息弹窗 -->\n\t\t<view class=\"add-info-modal\" v-if=\"showAddInfo\" @click=\"closeAddInfoModal\">\n\t\t\t<view class=\"add-info-content\" @click.stop>\n\t\t\t\t<view class=\"add-info-header\">\n\t\t\t\t\t<text class=\"add-info-title\">新增发货信息</text>\n\t\t\t\t\t<text class=\"close-btn\" @click=\"closeAddInfoModal\">✕</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"add-info-form\">\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">数量(米)</text>\n\t\t\t\t\t\t<input\n\t\t\t\t\t\t\tclass=\"form-input\"\n\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\tv-model=\"addInfoForm.length\"\n\t\t\t\t\t\t\tplaceholder=\"请输入数量\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">重量(千克)</text>\n\t\t\t\t\t\t<input\n\t\t\t\t\t\t\tclass=\"form-input\"\n\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\tv-model=\"addInfoForm.weight_total\"\n\t\t\t\t\t\t\tplaceholder=\"请输入重量\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">价格</text>\n\t\t\t\t\t\t<input\n\t\t\t\t\t\t\tclass=\"form-input\"\n\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\tv-model=\"addInfoForm.actual_price\"\n\t\t\t\t\t\t\tplaceholder=\"请输入价格\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"add-info-actions\">\n\t\t\t\t\t<button class=\"cancel-btn\" @click=\"closeAddInfoModal\">取消</button>\n\t\t\t\t\t<button class=\"confirm-btn\" @click=\"submitAddInfo\" :disabled=\"isSubmitting\">\n\t\t\t\t\t\t{{ isSubmitting ? '提交中...' : '确认' }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 图片预览弹窗 -->\n\t\t<view class=\"preview-modal\" v-if=\"showPreview\" @click=\"closePreview\">\n\t\t\t<view class=\"preview-content\" @click.stop>\n\t\t\t\t<view class=\"preview-header\">\n\t\t\t\t\t<text class=\"preview-title\">图片预览</text>\n\t\t\t\t\t<text class=\"close-btn\" @click=\"closePreview\">✕</text>\n\t\t\t\t</view>\n\t\t\t\t<swiper \n\t\t\t\t\tclass=\"preview-swiper\" \n\t\t\t\t\t:current=\"currentPreviewIndex\"\n\t\t\t\t\t@change=\"onSwiperChange\"\n\t\t\t\t>\n\t\t\t\t\t<swiper-item v-for=\"image in imageList\" :key=\"image.id\">\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t\tclass=\"preview-image\"\n\t\t\t\t\t\t\t:src=\"getImageUrl(image.image_path, image.id)\"\n\t\t\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</swiper-item>\n\t\t\t\t</swiper>\n\t\t\t\t<view class=\"preview-info\">\n\t\t\t\t\t<text class=\"preview-name\">{{ currentPreviewImage?.image_name }}</text>\n\t\t\t\t\t<text class=\"preview-index\">{{ currentPreviewIndex + 1 }} / {{ imageList.length }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { showSuccess, showError, showConfirm } from '../../utils/helpers.js';\n\timport userManager from '../../utils/userManager.js';\n\timport dataCacheManager from '../../utils/dataCache.js';\n\timport performanceMonitor from '../../utils/performanceMonitor.js';\n\timport urlConfig from '../../utils/urlConfig.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torderNumber: '',\n\t\t\t\tuserInfo: {},\n\t\t\t\timageList: [],\n\t\t\t\tselectedImages: [],\n\t\t\t\tisSelectMode: false,\n\t\t\t\tisLoading: false,\n\t\t\t\tisRefreshing: false,\n\t\t\t\tshowPreview: false,\n\t\t\t\tcurrentPreviewIndex: 0,\n\t\t\t\t// 分页相关数据\n\t\t\t\tpagination: {\n\t\t\t\t\tpage: 1,\n\t\t\t\t\tlimit: 10,\n\t\t\t\t\ttotal: 0,\n\t\t\t\t\ttotalPages: 0,\n\t\t\t\t\thasNext: false,\n\t\t\t\t\thasPrev: false\n\t\t\t\t},\n\t\t\t\t// 活动的计时器列表\n\t\t\t\tactiveTimers: new Set(),\n\t\t\t\t// 发货单号相关\n\t\t\t\tshippingNumbers: [],\n\t\t\t\tcurrentShippingNumber: null, // 初始为null，等待发货单号加载完成后设置\n\t\t\t\t// 发货详细信息\n\t\t\t\tshippingDetails: [],\n\t\t\t\t// 新增信息相关\n\t\t\t\tshowAddInfo: false,\n\t\t\t\tisSubmitting: false,\n\t\t\t\taddInfoForm: {\n\t\t\t\t\tlength: '',\n\t\t\t\t\tweight_total: '',\n\t\t\t\t\tactual_price: ''\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tcurrentPreviewImage() {\n\t\t\t\treturn this.imageList[this.currentPreviewIndex];\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 是否有图片数据\n\t\t\t */\n\t\t\thasImages() {\n\t\t\t\treturn this.imageList && Array.isArray(this.imageList) && this.imageList.length > 0;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 是否应该显示空状态\n\t\t\t */\n\t\t\tshouldShowEmpty() {\n\t\t\t\treturn !this.isLoading && !this.hasImages;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 获取发货单号列表\n\t\t\t */\n\t\t\tasync loadShippingNumbers() {\n\t\t\t\ttry {\n\t\t\t\t\tconst requestConfig = userManager.createAuthRequest({\n\t\t\t\t\t\turl: urlConfig.getApiUrl(`/api/orders/${encodeURIComponent(this.orderNumber)}/shipping-numbers`),\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\ttimeout: 10000\n\t\t\t\t\t});\n\n\t\t\t\t\tconst response = await uni.request(requestConfig);\n\n\t\t\t\t\tif (response.statusCode === 200 && response.data.success) {\n\t\t\t\t\t\tthis.shippingNumbers = response.data.data;\n\t\t\t\t\t\t// 无论有几个发货单号，都默认选择\"全部\"，避免过滤条件导致图片不显示\n\t\t\t\t\t\tthis.currentShippingNumber = 'ALL';\n\t\t\t\t\t\tconsole.log('📦 获取发货单号列表成功:', this.shippingNumbers);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(response.data?.message || '获取发货单号失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 获取发货单号列表失败:', error);\n\t\t\t\t\t// 如果获取失败，设置默认值，但仍然选择\"全部\"\n\t\t\t\t\tthis.shippingNumbers = ['DEFAULT'];\n\t\t\t\t\tthis.currentShippingNumber = 'ALL';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取发货详细信息\n\t\t\t */\n\t\t\tasync loadShippingDetails() {\n\t\t\t\ttry {\n\t\t\t\t\t// 构建查询参数\n\t\t\t\t\tlet queryParams = '';\n\t\t\t\t\tif (this.currentShippingNumber && this.currentShippingNumber !== 'ALL') {\n\t\t\t\t\t\tqueryParams = `?shipping_number=${encodeURIComponent(this.currentShippingNumber)}`;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst requestConfig = userManager.createAuthRequest({\n\t\t\t\t\t\turl: urlConfig.getApiUrl(`/api/orders/${encodeURIComponent(this.orderNumber)}/shipping-details${queryParams}`),\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\ttimeout: 10000\n\t\t\t\t\t});\n\n\t\t\t\t\tconst response = await uni.request(requestConfig);\n\n\t\t\t\t\tif (response.statusCode === 200 && response.data.success) {\n\t\t\t\t\t\tthis.shippingDetails = response.data.data;\n\t\t\t\t\t\tconsole.log('📋 获取发货详细信息成功:', this.shippingDetails);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('获取发货详细信息失败:', response.data?.message);\n\t\t\t\t\t\tthis.shippingDetails = [];\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 获取发货详细信息失败:', error);\n\t\t\t\t\tthis.shippingDetails = [];\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 格式化日期\n\t\t\t */\n\t\t\tformatDate(dateString) {\n\t\t\t\tif (!dateString) return '-';\n\t\t\t\ttry {\n\t\t\t\t\tconst date = new Date(dateString);\n\t\t\t\t\tconst year = date.getFullYear();\n\t\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('日期格式化失败:', error);\n\t\t\t\t\treturn '-';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 切换发货单号\n\t\t\t */\n\t\t\tasync switchShippingNumber(shippingNumber) {\n\t\t\t\tif (this.currentShippingNumber === shippingNumber) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.currentShippingNumber = shippingNumber;\n\t\t\t\tconsole.log('🔄 切换发货单号:', shippingNumber);\n\n\t\t\t\t// 重新加载发货详细信息和图片列表\n\t\t\t\tthis.pagination.page = 1;\n\t\t\t\tawait Promise.all([\n\t\t\t\t\tthis.loadShippingDetails(),\n\t\t\t\t\tthis.loadImages(true)\n\t\t\t\t]);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 跳转到拍照上传页面\n\t\t\t */\n\t\t\tgoToCamera() {\n\t\t\t\tconst currentShipping = this.currentShippingNumber === 'ALL'\n\t\t\t\t\t? (this.shippingNumbers[0] || 'DEFAULT')\n\t\t\t\t\t: this.currentShippingNumber;\n\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/camera/camera?orderNumber=${encodeURIComponent(this.orderNumber)}&shippingNumber=${encodeURIComponent(currentShipping)}`,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('跳转到拍照页面');\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\tshowError('页面跳转失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 分页相关方法\n\t\t\t */\n\t\t\t// 上一页\n\t\t\tgoToPrevPage() {\n\t\t\t\tif (this.pagination.hasPrev) {\n\t\t\t\t\tthis.pagination.page--;\n\t\t\t\t\tthis.loadImages();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 下一页\n\t\t\tgoToNextPage() {\n\t\t\t\tif (this.pagination.hasNext) {\n\t\t\t\t\tthis.pagination.page++;\n\t\t\t\t\tthis.loadImages();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 跳转到指定页\n\t\t\tgoToPage(pageNum) {\n\t\t\t\tif (pageNum !== this.pagination.page && pageNum >= 1 && pageNum <= this.pagination.totalPages) {\n\t\t\t\t\tthis.pagination.page = pageNum;\n\t\t\t\t\tthis.loadImages();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取页码数组\n\t\t\tgetPageNumbers() {\n\t\t\t\tconst { page, totalPages } = this.pagination;\n\t\t\t\tconst numbers = [];\n\n\t\t\t\t// 显示当前页前后各2页\n\t\t\t\tconst start = Math.max(1, page - 2);\n\t\t\t\tconst end = Math.min(totalPages, page + 2);\n\n\t\t\t\tfor (let i = start; i <= end; i++) {\n\t\t\t\t\tnumbers.push(i);\n\t\t\t\t}\n\n\t\t\t\treturn numbers;\n\t\t\t},\n\n\t\t\t// 格式化时间\n\t\t\tformatTime(timeStr) {\n\t\t\t\tif (!timeStr) return '未知';\n\t\t\t\ttry {\n\t\t\t\t\tconst date = new Date(timeStr);\n\t\t\t\t\treturn date.toLocaleString('zh-CN', {\n\t\t\t\t\t\tyear: 'numeric',\n\t\t\t\t\t\tmonth: '2-digit',\n\t\t\t\t\t\tday: '2-digit',\n\t\t\t\t\t\thour: '2-digit',\n\t\t\t\t\t\tminute: '2-digit'\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn '未知';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取图片URL\n\t\t\tgetImageUrl(imagePath, imageId) {\n\t\t\t\treturn urlConfig.getImageUrl(imagePath, imageId);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理图片点击\n\t\t\t */\n\t\t\thandleImageClick(event) {\n\t\t\t\tconst { image, index } = event;\n\t\t\t\tif (this.isSelectMode) {\n\t\t\t\t\tthis.toggleImageSelection(image.id);\n\t\t\t\t} else {\n\t\t\t\t\tthis.showImagePreview(index);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理图片长按\n\t\t\t */\n\t\t\thandleImageLongPress(image) {\n\t\t\t\tif (!this.isSelectMode) {\n\t\t\t\t\tthis.isSelectMode = true;\n\t\t\t\t\tthis.selectedImages = [image.id];\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 切换选择模式\n\t\t\t */\n\t\t\ttoggleSelectMode() {\n\t\t\t\tthis.isSelectMode = !this.isSelectMode;\n\t\t\t\tif (!this.isSelectMode) {\n\t\t\t\t\tthis.selectedImages = [];\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 切换图片选择状态\n\t\t\t */\n\t\t\ttoggleImageSelection(imageId) {\n\t\t\t\tconst index = this.selectedImages.indexOf(imageId);\n\t\t\t\tif (index > -1) {\n\t\t\t\t\tthis.selectedImages.splice(index, 1);\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedImages.push(imageId);\n\t\t\t\t}\n\n\t\t\t\t// 如果没有选中的图片，退出选择模式\n\t\t\t\tif (this.selectedImages.length === 0) {\n\t\t\t\t\tthis.isSelectMode = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 检查图片是否被选中\n\t\t\t */\n\t\t\tisImageSelected(imageId) {\n\t\t\t\treturn this.selectedImages.includes(imageId);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 显示图片预览\n\t\t\t */\n\t\t\tshowImagePreview(index) {\n\t\t\t\tthis.currentPreviewIndex = index;\n\t\t\t\tthis.showPreview = true;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 关闭图片预览\n\t\t\t */\n\t\t\tclosePreview() {\n\t\t\t\tthis.showPreview = false;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 轮播图切换\n\t\t\t */\n\t\t\tonSwiperChange(e) {\n\t\t\t\tthis.currentPreviewIndex = e.detail.current;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理删除选中图片\n\t\t\t */\n\t\t\tasync handleDeleteSelected() {\n\t\t\t\tconsole.log('🗑️ 开始删除操作，选中图片:', this.selectedImages);\n\n\t\t\t\tif (this.selectedImages.length === 0) {\n\t\t\t\t\tshowError('请先选择要删除的图片');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst confirmed = await showConfirm(\n\t\t\t\t\t'确认删除',\n\t\t\t\t\t`确定要删除选中的 ${this.selectedImages.length} 张图片吗？此操作不可恢复。`\n\t\t\t\t);\n\n\t\t\t\tconsole.log('🗑️ 用户确认删除:', confirmed);\n\n\t\t\t\tif (confirmed) {\n\t\t\t\t\tawait this.deleteImages();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 删除图片\n\t\t\t */\n\t\t\tasync deleteImages() {\n\t\t\t\tconsole.log('🗑️ 执行删除请求，图片IDs:', this.selectedImages);\n\n\t\t\t\ttry {\n\t\t\t\t\tconst response = await this.deleteImagesRequest();\n\t\t\t\t\tconsole.log('🗑️ 删除响应:', response);\n\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tshowSuccess(`成功删除 ${this.selectedImages.length} 张图片`);\n\t\t\t\t\t\tthis.selectedImages = [];\n\t\t\t\t\t\tthis.isSelectMode = false;\n\n\t\t\t\t\t\t// 清除相关缓存（包括所有分页的缓存）\n\t\t\t\t\t\tfor (let page = 1; page <= this.pagination.totalPages; page++) {\n\t\t\t\t\t\t\tconst cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${page}`;\n\t\t\t\t\t\t\tdataCacheManager.removeCache('images', cacheKey);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 重置到第一页\n\t\t\t\t\t\tthis.pagination.page = 1;\n\n\t\t\t\t\t\t// 重新加载图片列表\n\t\t\t\t\t\tawait this.loadImages(true);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('🗑️ 删除失败:', response);\n\t\t\t\t\t\tshowError(response.message || '删除失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('🗑️ 删除图片异常:', error);\n\t\t\t\t\tshowError('删除失败：' + error.message);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 删除图片请求\n\t\t\t */\n\t\t\tasync deleteImagesRequest() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tconst requestConfig = userManager.createAuthRequest({\n\t\t\t\t\t\turl: urlConfig.getApiUrl('/api/images/delete'),\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\timageIds: this.selectedImages\n\t\t\t\t\t\t},\n\t\t\t\t\t\ttimeout: 15000,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t\tresolve(res.data);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treject(new Error(`服务器错误 (${res.statusCode})`));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('删除图片请求失败:', err);\n\t\t\t\t\t\t\treject(new Error('网络连接失败'));\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tuni.request(requestConfig);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 加载图片列表（支持分页）\n\t\t\t */\n\t\t\tasync loadImages(forceRefresh = false) {\n\t\t\t\tif (!this.orderNumber || !this.userInfo.factory_name || this.currentShippingNumber === null) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 开始性能监控\n\t\t\t\tconst timerName = `loadImages_${this.orderNumber}_page${this.pagination.page}`;\n\n\t\t\t\t// 清理可能存在的同名计时器\n\t\t\t\tif (this.activeTimers.has(timerName)) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tperformanceMonitor.endTimer(timerName, 'loadTime');\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.warn('清理旧计时器失败:', error);\n\t\t\t\t\t}\n\t\t\t\t\tthis.activeTimers.delete(timerName);\n\t\t\t\t}\n\n\t\t\t\t// 启动新计时器\n\t\t\t\tperformanceMonitor.startTimer(timerName);\n\t\t\t\tthis.activeTimers.add(timerName);\n\n\t\t\t\tthis.isLoading = true;\n\n\t\t\t\ttry {\n\t\t\t\t\t// 构建缓存键，包含分页信息和发货单号\n\t\t\t\t\tconst shippingKey = this.currentShippingNumber || 'ALL';\n\t\t\t\t\tconst cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_${shippingKey}_page${this.pagination.page}`;\n\t\t\t\t\tconst imageData = await dataCacheManager.getData(\n\t\t\t\t\t\t'images',\n\t\t\t\t\t\tcacheKey,\n\t\t\t\t\t\t() => this.getImagesRequest(),\n\t\t\t\t\t\t{ forceRefresh, ttl: 300 } // 5分钟缓存\n\t\t\t\t\t);\n\n\t\t\t\t\tif (imageData.success) {\n\t\t\t\t\t\tthis.imageList = imageData.data || [];\n\n\t\t\t\t\t\t// 更新分页信息\n\t\t\t\t\t\tif (imageData.pagination) {\n\t\t\t\t\t\t\tthis.pagination = {\n\t\t\t\t\t\t\t\t...this.pagination,\n\t\t\t\t\t\t\t\t...imageData.pagination\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconsole.log(`图片列表加载成功: 第${this.pagination.page}页, ${this.imageList.length}/${this.pagination.total} 张图片`);\n\n\t\t\t\t\t\t// 记录加载性能\n\t\t\t\t\t\tconst loadDuration = performanceMonitor.endTimer(timerName, 'loadTime');\n\t\t\t\t\t\tthis.activeTimers.delete(timerName);\n\t\t\t\t\t\tperformanceMonitor.recordLoadPerformance('imageList', this.imageList.length, loadDuration);\n\n\t\t\t\t\t\t// 记录缓存命中情况\n\t\t\t\t\t\tperformanceMonitor.recordCacheHit('data', !forceRefresh);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('获取图片列表失败:', imageData.message);\n\t\t\t\t\t\tthis.imageList = [];\n\t\t\t\t\t\tthis.pagination.total = 0;\n\t\t\t\t\t\tthis.pagination.totalPages = 0;\n\t\t\t\t\t\tperformanceMonitor.endTimer(timerName, 'loadTime');\n\t\t\t\t\t\tthis.activeTimers.delete(timerName);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载图片列表失败:', error);\n\t\t\t\t\tthis.imageList = [];\n\t\t\t\t\tthis.pagination.total = 0;\n\t\t\t\t\tthis.pagination.totalPages = 0;\n\t\t\t\t\tshowError('加载图片列表失败');\n\t\t\t\t\tperformanceMonitor.endTimer(timerName, 'loadTime');\n\t\t\t\t\tthis.activeTimers.delete(timerName);\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取图片列表请求（支持分页）\n\t\t\t */\n\t\t\tasync getImagesRequest() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\t// 构建查询参数\n\t\t\t\t\tlet queryParams = `page=${this.pagination.page}&limit=${this.pagination.limit}`;\n\t\t\t\t\tif (this.currentShippingNumber && this.currentShippingNumber !== 'ALL') {\n\t\t\t\t\t\tqueryParams += `&shipping_number=${encodeURIComponent(this.currentShippingNumber)}`;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst requestConfig = userManager.createAuthRequest({\n\t\t\t\t\t\turl: urlConfig.getApiUrl(`/api/images/order/${encodeURIComponent(this.orderNumber)}?${queryParams}`),\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\ttimeout: 10000,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t\tresolve(res.data);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treject(new Error(`服务器错误 (${res.statusCode})`));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('获取图片列表请求失败:', err);\n\t\t\t\t\t\t\treject(new Error('网络连接失败'));\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tuni.request(requestConfig);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 下拉刷新\n\t\t\t */\n\t\t\tasync handleRefresh() {\n\t\t\t\tthis.isRefreshing = true;\n\t\t\t\ttry {\n\t\t\t\t\t// 强制刷新，不使用缓存\n\t\t\t\t\tawait this.loadImages(true);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('刷新失败:', error);\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isRefreshing = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取图片URL - 同步版本，用于模板渲染\n\t\t\t */\n\t\t\tgetImageUrl(imagePath, imageId) {\n\t\t\t\treturn urlConfig.getImageUrl(imagePath, imageId);\n\t\t\t},\n\n\n\n\t\t\t/**\n\t\t\t * 处理图片加载错误\n\t\t\t */\n\t\t\thandleImageError(e) {\n\t\t\t\tconsole.error('图片加载失败:', e);\n\t\t\t\tconsole.error('失败的图片URL:', e.target?.src || '未知');\n\t\t\t\tconsole.error('图片元素:', e.target);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 格式化时间\n\t\t\t */\n\t\t\tformatTime(timeStr) {\n\t\t\t\tif (!timeStr) return '未知';\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst date = new Date(timeStr);\n\t\t\t\t\treturn date.toLocaleString();\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn '未知';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 显示性能报告（开发调试用）\n\t\t\t */\n\t\t\tshowPerformanceReport() {\n\t\t\t\tconst report = performanceMonitor.generateReport();\n\t\t\t\tconsole.log(report);\n\n\t\t\t\t// 在开发环境中显示性能统计\n\t\t\t\tif (process.env.NODE_ENV === 'development') {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '性能报告',\n\t\t\t\t\t\tcontent: report,\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理图片上传事件\n\t\t\t */\n\t\t\thandleImageUploaded(data) {\n\t\t\t\tif (data.orderNumber === this.orderNumber) {\n\t\t\t\t\tconsole.log('收到图片上传事件，刷新图片列表');\n\t\t\t\t\t// 清除缓存并刷新图片列表\n\t\t\t\t\tconst cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}`;\n\t\t\t\t\tdataCacheManager.removeCache('images', cacheKey);\n\t\t\t\t\tthis.loadImages(true);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 清理所有活动的计时器\n\t\t\t */\n\t\t\tcleanupActiveTimers() {\n\t\t\t\tconsole.log('清理活动计时器，数量:', this.activeTimers.size);\n\t\t\t\tfor (const timerName of this.activeTimers) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tperformanceMonitor.endTimer(timerName, 'loadTime');\n\t\t\t\t\t\tconsole.log('已清理计时器:', timerName);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.warn('清理计时器失败:', timerName, error);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.activeTimers.clear();\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 显示新增信息弹窗\n\t\t\t */\n\t\t\tshowAddInfoModal() {\n\t\t\t\tthis.showAddInfo = true;\n\t\t\t\t// 重置表单\n\t\t\t\tthis.addInfoForm = {\n\t\t\t\t\tlength: '',\n\t\t\t\t\tweight_total: '',\n\t\t\t\t\tactual_price: ''\n\t\t\t\t};\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 关闭新增信息弹窗\n\t\t\t */\n\t\t\tcloseAddInfoModal() {\n\t\t\t\tthis.showAddInfo = false;\n\t\t\t\tthis.isSubmitting = false;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 提交新增信息\n\t\t\t */\n\t\t\tasync submitAddInfo() {\n\t\t\t\tthis.isSubmitting = true;\n\n\t\t\t\ttry {\n\t\t\t\t\tconst requestConfig = userManager.createAuthRequest({\n\t\t\t\t\t\turl: urlConfig.getApiUrl('/api/orders/add-shipping-info'),\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\torderNumber: this.orderNumber,\n\t\t\t\t\t\t\tshippingNumber: this.currentShippingNumber === 'ALL' ?\n\t\t\t\t\t\t\t\t(this.shippingNumbers[0] || 'DEFAULT') : this.currentShippingNumber,\n\t\t\t\t\t\t\tlength: this.addInfoForm.length ? parseFloat(this.addInfoForm.length) : null,\n\t\t\t\t\t\t\tweight_total: this.addInfoForm.weight_total ? parseFloat(this.addInfoForm.weight_total) : null,\n\t\t\t\t\t\t\tactual_price: this.addInfoForm.actual_price ? parseFloat(this.addInfoForm.actual_price) : null\n\t\t\t\t\t\t},\n\t\t\t\t\t\ttimeout: 10000\n\t\t\t\t\t});\n\n\t\t\t\t\tconst response = await uni.request(requestConfig);\n\n\t\t\t\t\tif (response.statusCode === 200 && response.data.success) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '添加成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 关闭弹窗\n\t\t\t\t\t\tthis.closeAddInfoModal();\n\n\t\t\t\t\t\t// 重新加载发货详细信息\n\t\t\t\t\t\tawait this.loadShippingDetails();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(response.data?.message || '添加失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 添加发货信息失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '添加失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isSubmitting = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 初始化\n\t\t\t */\n\t\t\tasync init() {\n\t\t\t\t// 使用用户管理器检查登录状态\n\t\t\t\tif (userManager.requireLogin()) {\n\t\t\t\t\tthis.userInfo = userManager.getUserInfo();\n\t\t\t\t\t// 并行加载发货单号列表和发货详细信息\n\t\t\t\t\tawait Promise.all([\n\t\t\t\t\t\tthis.loadShippingNumbers(),\n\t\t\t\t\t\tthis.loadShippingDetails()\n\t\t\t\t\t]);\n\t\t\t\t\t// 发货单号加载完成后再加载图片列表\n\t\t\t\t\tawait this.loadImages();\n\t\t\t\t}\n\t\t\t\t// 如果未登录，requireLogin会自动跳转到登录页面\n\t\t\t}\n\t\t},\n\n\t\tonLoad(options) {\n\t\t\tthis.orderNumber = options.orderNumber || '';\n\t\t\tif (!this.orderNumber) {\n\t\t\t\tshowError('订单号不能为空');\n\t\t\t\tuni.navigateBack();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.init();\n\n\t\t\t// 监听图片上传事件\n\t\t\tuni.$on('imageUploaded', this.handleImageUploaded);\n\t\t},\n\n\t\tonShow() {\n\t\t\t// 页面显示时强制刷新图片列表（不使用缓存）\n\t\t\tif (this.orderNumber && this.userInfo.factory_name) {\n\t\t\t\t// 清除所有分页的缓存，确保获取最新数据\n\t\t\t\tfor (let page = 1; page <= 10; page++) { // 清除前10页的缓存\n\t\t\t\t\tconst cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${page}`;\n\t\t\t\t\tdataCacheManager.removeCache('images', cacheKey);\n\t\t\t\t}\n\n\t\t\t\t// 重置到第一页并强制刷新\n\t\t\t\tthis.pagination.page = 1;\n\t\t\t\tthis.loadImages(true);\n\t\t\t}\n\t\t},\n\n\t\tonBackPress() {\n\t\t\t// 如果在选择模式，先退出选择模式\n\t\t\tif (this.isSelectMode) {\n\t\t\t\tthis.isSelectMode = false;\n\t\t\t\tthis.selectedImages = [];\n\t\t\t\treturn true; // 阻止默认返回行为\n\t\t\t}\n\n\t\t\t// 清理活动的计时器\n\t\t\tthis.cleanupActiveTimers();\n\n\t\t\treturn false; // 允许默认返回行为\n\t\t},\n\n\t\tonUnload() {\n\t\t\t// 清理事件监听\n\t\t\tuni.$off('imageUploaded', this.handleImageUploaded);\n\n\t\t\t// 清理所有活动的计时器\n\t\t\tthis.cleanupActiveTimers();\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.image-manage-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t\tpadding: 20rpx;\n\t}\n\n\t.header {\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tbox-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\n\t}\n\n\t/* 发货单号选择器样式 */\n\t.shipping-selector {\n\t\tmargin: 0 20rpx 30rpx;\n\t\tbackground: white;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.selector-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.title-icon {\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.title-text {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t}\n\n\t.shipping-tabs {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 15rpx;\n\t}\n\n\t.shipping-tab {\n\t\tpadding: 15rpx 25rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 25rpx;\n\t\tborder: 2rpx solid #e9ecef;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.shipping-tab.active {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tborder-color: #667eea;\n\t\tcolor: white;\n\t}\n\n\t.tab-text {\n\t\tfont-size: 26rpx;\n\t\tfont-weight: 500;\n\t}\n\n\t.shipping-tab.active .tab-text {\n\t\tcolor: white;\n\t}\n\n\t/* 当前发货单号显示 */\n\t.current-shipping {\n\t\tmargin: 0 20rpx 30rpx;\n\t\tpadding: 20rpx 30rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 15rpx;\n\t\tborder-left: 4rpx solid #4f46e5;\n\t}\n\n\t.shipping-info {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t\tfont-weight: 500;\n\t}\n\n\t/* 发货详细信息样式 */\n\t.shipping-details {\n\t\tmargin: 0 20rpx 30rpx;\n\t\tbackground: white;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.details-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 25rpx;\n\t\tpadding-bottom: 20rpx;\n\t\tborder-bottom: 2rpx solid #f0f0f0;\n\t}\n\n\t.header-left {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.details-icon {\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.details-title {\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t}\n\n\t.add-info-btn {\n\t\tbackground: linear-gradient(135deg, #4CAF50, #45a049);\n\t\tborder: none;\n\t\tborder-radius: 15rpx;\n\t\tpadding: 15rpx 25rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(76, 175, 80, 0.3);\n\t}\n\n\t.add-icon {\n\t\tfont-size: 24rpx;\n\t\tcolor: white;\n\t\tmargin-right: 8rpx;\n\t}\n\n\t.add-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: white;\n\t\tfont-weight: 500;\n\t}\n\n\t.details-cards {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 20rpx;\n\t}\n\n\t.detail-card {\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 15rpx;\n\t\tpadding: 25rpx;\n\t\tborder-left: 6rpx solid #4f46e5;\n\t\tposition: relative;\n\t}\n\n\t.card-row {\n\t\tdisplay: flex;\n\t\tmargin-bottom: 15rpx;\n\t\tgap: 20rpx;\n\t}\n\n\t.card-row:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.info-item {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 8rpx;\n\t}\n\n\t.info-item.full-width {\n\t\tflex: 2;\n\t}\n\n\t.info-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tfont-weight: 500;\n\t}\n\n\t.info-value {\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tword-break: break-all;\n\t}\n\n\t.remark-text {\n\t\tcolor: #666;\n\t\tfont-style: italic;\n\t\tline-height: 1.4;\n\t\tmax-height: 120rpx;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-line-clamp: 3;\n\t\t-webkit-box-orient: vertical;\n\t}\n\n\t.price-text {\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tfont-size: 28rpx;\n\t}\n\n\t.order-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex: 1;\n\t}\n\n\t.order-icon {\n\t\tfont-size: 50rpx;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.order-details {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.order-label {\n\t\tfont-size: 26rpx;\n\t\tcolor: rgba(255, 255, 255, 0.8);\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.order-number {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t}\n\n\t.image-count {\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tborder-radius: 30rpx;\n\t\tpadding: 15rpx 25rpx;\n\t}\n\n\t.count-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #ffffff;\n\t}\n\n\t.action-section {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.action-btn {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder: none;\n\t\tborder-radius: 12rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tgap: 15rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.add-btn {\n\t\tbackground: linear-gradient(135deg, #4CAF50, #45a049);\n\t\tcolor: #ffffff;\n\t}\n\n\t.select-btn {\n\t\tbackground: linear-gradient(135deg, #2196F3, #1976D2);\n\t\tcolor: #ffffff;\n\t}\n\n\t.select-btn.active {\n\t\tbackground: linear-gradient(135deg, #FF9800, #F57C00);\n\t}\n\n\t.delete-btn {\n\t\tbackground: linear-gradient(135deg, #f44336, #d32f2f);\n\t\tcolor: #ffffff;\n\t}\n\n\t.delete-btn.disabled {\n\t\tbackground: #cccccc;\n\t\tcolor: #999999;\n\t}\n\n\t.btn-icon {\n\t\tfont-size: 32rpx;\n\t}\n\n\t.select-tip {\n\t\tbackground: #e3f2fd;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tborder-left: 6rpx solid #2196F3;\n\t}\n\n\t.tip-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #1976D2;\n\t}\n\n\t.image-grid-section {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 30rpx;\n\t\tflex: 1;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t/* 图片网格样式 */\n\t.image-grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: 1fr 1fr;\n\t\tgap: 20rpx;\n\t\tpadding-bottom: 20rpx;\n\t}\n\n\t.image-item {\n\t\tposition: relative;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t\taspect-ratio: 1;\n\t}\n\n\t.select-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.3);\n\t\tz-index: 2;\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tjustify-content: flex-end;\n\t\tpadding: 20rpx;\n\t}\n\n\t.select-checkbox {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder-radius: 50%;\n\t\tborder: 4rpx solid #fff;\n\t\tbackground: rgba(255, 255, 255, 0.8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.select-checkbox.selected {\n\t\tbackground: #007aff;\n\t\tborder-color: #007aff;\n\t}\n\n\t.checkbox-icon {\n\t\tcolor: #fff;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t}\n\n\t.image-info {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbackground: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\n\t\tpadding: 30rpx 20rpx 20rpx;\n\t\tcolor: #fff;\n\t}\n\n\t.image-name {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 8rpx;\n\t\tdisplay: block;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.image-time {\n\t\tfont-size: 20rpx;\n\t\topacity: 0.8;\n\t\tdisplay: block;\n\t}\n\n\t/* 分页样式 */\n\t.pagination-section {\n\t\tmargin-top: 30rpx;\n\t\tpadding-top: 30rpx;\n\t\tborder-top: 2rpx solid #f0f0f0;\n\t}\n\n\t.pagination-info {\n\t\ttext-align: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.page-info {\n\t\tfont-size: 26rpx;\n\t\tcolor: #333;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.total-info {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\n\t.pagination-controls {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tgap: 20rpx;\n\t}\n\n\t.page-btn {\n\t\tpadding: 16rpx 32rpx;\n\t\tborder: 2rpx solid #007aff;\n\t\tborder-radius: 8rpx;\n\t\tbackground: #fff;\n\t\tcolor: #007aff;\n\t\tfont-size: 24rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.page-btn:not([disabled]):active {\n\t\tbackground: #007aff;\n\t\tcolor: #fff;\n\t}\n\n\t.page-btn[disabled] {\n\t\tborder-color: #ddd;\n\t\tcolor: #ccc;\n\t\tbackground: #f5f5f5;\n\t}\n\n\t.page-numbers {\n\t\tdisplay: flex;\n\t\tgap: 10rpx;\n\t}\n\n\t.page-number {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder-radius: 8rpx;\n\t\tborder: 2rpx solid #ddd;\n\t\tbackground: #fff;\n\t\tcolor: #333;\n\t\tfont-size: 24rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.page-number.current {\n\t\tbackground: #007aff;\n\t\tborder-color: #007aff;\n\t\tcolor: #fff;\n\t}\n\n\t.page-number:not(.current):active {\n\t\tbackground: #f0f0f0;\n\t}\n\n\t.loading-section {\n\t\ttext-align: center;\n\t\tpadding: 80rpx 0;\n\t}\n\n\t.loading-spinner {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder: 4rpx solid #f3f3f3;\n\t\tborder-top: 4rpx solid #667eea;\n\t\tborder-radius: 50%;\n\t\tanimation: spin 1s linear infinite;\n\t\tmargin: 0 auto 20rpx;\n\t}\n\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999999;\n\t}\n\n\t/* 虚拟滚动组件的样式已在组件内部定义 */\n\n\t.empty-state {\n\t\ttext-align: center;\n\t\tpadding: 100rpx 0;\n\t}\n\n\t.empty-icon {\n\t\tfont-size: 100rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tdisplay: block;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666666;\n\t\tmargin-bottom: 15rpx;\n\t\tdisplay: block;\n\t}\n\n\t.empty-hint {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999999;\n\t\tdisplay: block;\n\t}\n\n\t.preview-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.9);\n\t\tz-index: 9999;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.preview-content {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.preview-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 40rpx 30rpx;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t}\n\n\t.preview-title {\n\t\tfont-size: 32rpx;\n\t\tcolor: #ffffff;\n\t\tfont-weight: bold;\n\t}\n\n\t.close-btn {\n\t\tfont-size: 40rpx;\n\t\tcolor: #ffffff;\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.preview-swiper {\n\t\tflex: 1;\n\t}\n\n\t.preview-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.preview-info {\n\t\tpadding: 30rpx;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\ttext-align: center;\n\t}\n\n\t.preview-name {\n\t\tfont-size: 28rpx;\n\t\tcolor: #ffffff;\n\t\tmargin-bottom: 10rpx;\n\t\tdisplay: block;\n\t}\n\n\t.preview-index {\n\t\tfont-size: 24rpx;\n\t\tcolor: rgba(255, 255, 255, 0.8);\n\t\tdisplay: block;\n\t}\n\n\t@keyframes spin {\n\t\t0% { transform: rotate(0deg); }\n\t\t100% { transform: rotate(360deg); }\n\t}\n\n\t/* 新增信息弹窗样式 */\n\t.add-info-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 1000;\n\t}\n\n\t.add-info-content {\n\t\tbackground: white;\n\t\tborder-radius: 20rpx;\n\t\twidth: 90%;\n\t\tmax-width: 600rpx;\n\t\tmax-height: 80%;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);\n\t}\n\n\t.add-info-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 2rpx solid #f0f0f0;\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t}\n\n\t.add-info-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: white;\n\t}\n\n\t.add-info-form {\n\t\tpadding: 30rpx;\n\t\tmax-height: 400rpx;\n\t\toverflow-y: auto;\n\t}\n\n\t.form-item {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.form-label {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 15rpx;\n\t\tfont-weight: 500;\n\t}\n\n\t.form-input {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 10rpx;\n\t\tpadding: 0 20rpx;\n\t\tfont-size: 28rpx;\n\t\tbackground: #f9f9f9;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.form-input:focus {\n\t\tborder-color: #667eea;\n\t\tbackground: white;\n\t}\n\n\t.add-info-actions {\n\t\tdisplay: flex;\n\t\tpadding: 20rpx 30rpx 30rpx;\n\t\tgap: 20rpx;\n\t}\n\n\t.cancel-btn {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tbackground: #f5f5f5;\n\t\tborder: none;\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\n\t.confirm-btn {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t\tborder: none;\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: white;\n\t\tfont-weight: 500;\n\t}\n\n\t.confirm-btn:disabled {\n\t\tbackground: #ccc;\n\t\tcolor: #999;\n\t}\n</style>\n", "import MiniProgramPage from 'D:/Desktop/Warehouse/front/pages/imageManage/imageManage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["userManager", "urlConfig", "uni", "showError", "showConfirm", "showSuccess", "dataCacheManager", "performanceMonitor"], "mappings": ";;;;;;;AAmWC,MAAK,YAAU;AAAA,EACd,OAAO;AACC,WAAA;AAAA,MACN,aAAa;AAAA,MACb,UAAU,CAAC;AAAA,MACX,WAAW,CAAC;AAAA,MACZ,gBAAgB,CAAC;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,aAAa;AAAA,MACb,qBAAqB;AAAA;AAAA,MAErB,YAAY;AAAA,QACX,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA,MACV;AAAA;AAAA,MAEA,kCAAkB,IAAI;AAAA;AAAA,MAEtB,iBAAiB,CAAC;AAAA,MAClB,uBAAuB;AAAA;AAAA;AAAA,MAEvB,iBAAiB,CAAC;AAAA;AAAA,MAElB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,QACZ,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,cAAc;AAAA,MACf;AAAA,IAAA;AAAA,EAEF;AAAA,EACA,UAAU;AAAA,IACT,sBAAsB;AACd,aAAA,KAAK,UAAU,KAAK,mBAAmB;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA,IAKA,YAAY;AACJ,aAAA,KAAK,aAAa,MAAM,QAAQ,KAAK,SAAS,KAAK,KAAK,UAAU,SAAS;AAAA,IACnF;AAAA;AAAA;AAAA;AAAA,IAKA,kBAAkB;AACjB,aAAO,CAAC,KAAK,aAAa,CAAC,KAAK;AAAA,IACjC;AAAA,EACD;AAAA,EACA,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,MAAM,sBAAsB;;AACvB,UAAA;AACG,cAAA,gBAAgBA,8BAAY,kBAAkB;AAAA,UACnD,KAAKC,gBAAAA,UAAU,UAAU,eAAe,mBAAmB,KAAK,WAAW,CAAC,mBAAmB;AAAA,UAC/F,QAAQ;AAAA,UACR,SAAS;AAAA,QAAA,CACT;AAED,cAAM,WAAW,MAAMC,cAAAA,MAAI,QAAQ,aAAa;AAEhD,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACpD,eAAA,kBAAkB,SAAS,KAAK;AAErC,eAAK,wBAAwB;AAC7BA,wBAAA,MAAY,MAAA,OAAA,4CAAA,kBAAkB,KAAK,eAAe;AAAA,QAAA,OAC5C;AACN,gBAAM,IAAI,QAAM,cAAS,SAAT,mBAAe,YAAW,UAAU;AAAA,QACrD;AAAA,eACQ,OAAO;AACfA,sBAAA,MAAc,MAAA,SAAA,4CAAA,iBAAiB,KAAK;AAE/B,aAAA,kBAAkB,CAAC,SAAS;AACjC,aAAK,wBAAwB;AAAA,MAC9B;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,sBAAsB;;AACvB,UAAA;AAEH,YAAI,cAAc;AAClB,YAAI,KAAK,yBAAyB,KAAK,0BAA0B,OAAO;AACvE,wBAAc,oBAAoB,mBAAmB,KAAK,qBAAqB,CAAC;AAAA,QACjF;AAEM,cAAA,gBAAgBF,8BAAY,kBAAkB;AAAA,UACnD,KAAKC,gBAAU,UAAA,UAAU,eAAe,mBAAmB,KAAK,WAAW,CAAC,oBAAoB,WAAW,EAAE;AAAA,UAC7G,QAAQ;AAAA,UACR,SAAS;AAAA,QAAA,CACT;AAED,cAAM,WAAW,MAAMC,cAAAA,MAAI,QAAQ,aAAa;AAEhD,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACpD,eAAA,kBAAkB,SAAS,KAAK;AACrCA,wBAAA,MAAY,MAAA,OAAA,4CAAA,kBAAkB,KAAK,eAAe;AAAA,QAAA,OAC5C;AACNA,8BAAa,MAAA,QAAA,4CAAA,gBAAe,cAAS,SAAT,mBAAe,OAAO;AAClD,eAAK,kBAAkB;QACxB;AAAA,eACQ,OAAO;AACfA,sBAAA,MAAc,MAAA,SAAA,4CAAA,iBAAiB,KAAK;AACpC,aAAK,kBAAkB;MACxB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,WAAW,YAAY;AACtB,UAAI,CAAC;AAAmB,eAAA;AACpB,UAAA;AACG,cAAA,OAAO,IAAI,KAAK,UAAU;AAC1B,cAAA,OAAO,KAAK;AACZ,cAAA,QAAQ,OAAO,KAAK,SAAA,IAAa,CAAC,EAAE,SAAS,GAAG,GAAG;AACnD,cAAA,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,eACtB,OAAO;AACfA,sBAAA,iEAAc,YAAY,KAAK;AACxB,eAAA;AAAA,MACR;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,qBAAqB,gBAAgB;AACtC,UAAA,KAAK,0BAA0B,gBAAgB;AAClD;AAAA,MACD;AAEA,WAAK,wBAAwB;AAC7BA,oBAAA,MAAA,MAAA,OAAA,4CAAY,cAAc,cAAc;AAGxC,WAAK,WAAW,OAAO;AACvB,YAAM,QAAQ,IAAI;AAAA,QACjB,KAAK,oBAAoB;AAAA,QACzB,KAAK,WAAW,IAAI;AAAA,MAAA,CACpB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,aAAa;AACN,YAAA,kBAAkB,KAAK,0BAA0B,QACnD,KAAK,gBAAgB,CAAC,KAAK,YAC5B,KAAK;AAERA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,oCAAoC,mBAAmB,KAAK,WAAW,CAAC,mBAAmB,mBAAmB,eAAe,CAAC;AAAA,QACnI,SAAS,MAAM;uFACF,SAAS;AAAA,QACtB;AAAA,QACA,MAAM,CAAC,QAAQ;AACdA,wBAAA,MAAA,MAAA,SAAA,4CAAc,SAAS,GAAG;AAC1BC,wBAAA,UAAU,QAAQ;AAAA,QACnB;AAAA,MAAA,CACA;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,eAAe;AACV,UAAA,KAAK,WAAW,SAAS;AAC5B,aAAK,WAAW;AAChB,aAAK,WAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGA,eAAe;AACV,UAAA,KAAK,WAAW,SAAS;AAC5B,aAAK,WAAW;AAChB,aAAK,WAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGA,SAAS,SAAS;AACb,UAAA,YAAY,KAAK,WAAW,QAAQ,WAAW,KAAK,WAAW,KAAK,WAAW,YAAY;AAC9F,aAAK,WAAW,OAAO;AACvB,aAAK,WAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGA,iBAAiB;AAChB,YAAM,EAAE,MAAM,eAAe,KAAK;AAClC,YAAM,UAAU,CAAA;AAGhB,YAAM,QAAQ,KAAK,IAAI,GAAG,OAAO,CAAC;AAClC,YAAM,MAAM,KAAK,IAAI,YAAY,OAAO,CAAC;AAEzC,eAAS,IAAI,OAAO,KAAK,KAAK,KAAK;AAClC,gBAAQ,KAAK,CAAC;AAAA,MACf;AAEO,aAAA;AAAA,IACR;AAAA;AAAA,IAGA,WAAW,SAAS;AACnB,UAAI,CAAC;AAAgB,eAAA;AACjB,UAAA;AACG,cAAA,OAAO,IAAI,KAAK,OAAO;AACtB,eAAA,KAAK,eAAe,SAAS;AAAA,UACnC,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,QAAA,CACR;AAAA,eACO,OAAO;AACR,eAAA;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGA,YAAY,WAAW,SAAS;AACxB,aAAAF,0BAAU,YAAY,WAAW,OAAO;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB,OAAO;AACjB,YAAA,EAAE,OAAO,MAAU,IAAA;AACzB,UAAI,KAAK,cAAc;AACjB,aAAA,qBAAqB,MAAM,EAAE;AAAA,MAAA,OAC5B;AACN,aAAK,iBAAiB,KAAK;AAAA,MAC5B;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,qBAAqB,OAAO;AACvB,UAAA,CAAC,KAAK,cAAc;AACvB,aAAK,eAAe;AACf,aAAA,iBAAiB,CAAC,MAAM,EAAE;AAAA,MAChC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,mBAAmB;AACb,WAAA,eAAe,CAAC,KAAK;AACtB,UAAA,CAAC,KAAK,cAAc;AACvB,aAAK,iBAAiB;MACvB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,qBAAqB,SAAS;AAC7B,YAAM,QAAQ,KAAK,eAAe,QAAQ,OAAO;AACjD,UAAI,QAAQ,IAAI;AACV,aAAA,eAAe,OAAO,OAAO,CAAC;AAAA,MAAA,OAC7B;AACD,aAAA,eAAe,KAAK,OAAO;AAAA,MACjC;AAGI,UAAA,KAAK,eAAe,WAAW,GAAG;AACrC,aAAK,eAAe;AAAA,MACrB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,gBAAgB,SAAS;AACjB,aAAA,KAAK,eAAe,SAAS,OAAO;AAAA,IAC5C;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB,OAAO;AACvB,WAAK,sBAAsB;AAC3B,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA,IAKA,eAAe;AACd,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA,IAKA,eAAe,GAAG;AACZ,WAAA,sBAAsB,EAAE,OAAO;AAAA,IACrC;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,uBAAuB;AAC5BC,oBAAA,MAAA,MAAA,OAAA,4CAAY,oBAAoB,KAAK,cAAc;AAE/C,UAAA,KAAK,eAAe,WAAW,GAAG;AACrCC,sBAAA,UAAU,YAAY;AACtB;AAAA,MACD;AAEA,YAAM,YAAY,MAAMC,cAAA;AAAA,QACvB;AAAA,QACA,YAAY,KAAK,eAAe,MAAM;AAAA,MAAA;AAGvCF,oBAAA,MAAA,MAAA,OAAA,4CAAY,eAAe,SAAS;AAEpC,UAAI,WAAW;AACd,cAAM,KAAK;MACZ;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,eAAe;AACpBA,oBAAA,MAAA,MAAA,OAAA,4CAAY,qBAAqB,KAAK,cAAc;AAEhD,UAAA;AACG,cAAA,WAAW,MAAM,KAAK;AAC5BA,sBAAA,MAAA,MAAA,OAAA,4CAAY,aAAa,QAAQ;AAEjC,YAAI,SAAS,SAAS;AACrBG,wBAAA,YAAY,QAAQ,KAAK,eAAe,MAAM,MAAM;AACpD,eAAK,iBAAiB;AACtB,eAAK,eAAe;AAGpB,mBAAS,OAAO,GAAG,QAAQ,KAAK,WAAW,YAAY,QAAQ;AACxD,kBAAA,WAAW,GAAG,KAAK,SAAS,YAAY,IAAI,KAAK,WAAW,QAAQ,IAAI;AAC7DC,4BAAAA,iBAAA,YAAY,UAAU,QAAQ;AAAA,UAChD;AAGA,eAAK,WAAW,OAAO;AAGjB,gBAAA,KAAK,WAAW,IAAI;AAAA,QAAA,OACpB;AACNJ,wBAAA,MAAA,MAAA,SAAA,4CAAc,aAAa,QAAQ;AACzBC,wBAAAA,UAAA,SAAS,WAAW,MAAM;AAAA,QACrC;AAAA,eACQ,OAAO;AACfD,sBAAA,MAAA,MAAA,SAAA,4CAAc,eAAe,KAAK;AACxBC,sBAAAA,UAAA,UAAU,MAAM,OAAO;AAAA,MAClC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,sBAAsB;AAC3B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjC,cAAA,gBAAgBH,8BAAY,kBAAkB;AAAA,UACnD,KAAKC,gBAAAA,UAAU,UAAU,oBAAoB;AAAA,UAC7C,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,UAAU,KAAK;AAAA,UAChB;AAAA,UACA,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACb,gBAAA,IAAI,eAAe,KAAK;AAC3B,sBAAQ,IAAI,IAAI;AAAA,YAAA,OACV;AACN,qBAAO,IAAI,MAAM,UAAU,IAAI,UAAU,GAAG,CAAC;AAAA,YAC9C;AAAA,UACD;AAAA,UACA,MAAM,CAAC,QAAQ;AACdC,0BAAA,MAAc,MAAA,SAAA,4CAAA,aAAa,GAAG;AACvB,mBAAA,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QAAA,CACA;AAEDA,4BAAI,QAAQ,aAAa;AAAA,MAAA,CACzB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,WAAW,eAAe,OAAO;AAClC,UAAA,CAAC,KAAK,eAAe,CAAC,KAAK,SAAS,gBAAgB,KAAK,0BAA0B,MAAM;AAC5F;AAAA,MACD;AAGA,YAAM,YAAY,cAAc,KAAK,WAAW,QAAQ,KAAK,WAAW,IAAI;AAG5E,UAAI,KAAK,aAAa,IAAI,SAAS,GAAG;AACjC,YAAA;AACgBK,mCAAAA,mBAAA,SAAS,WAAW,UAAU;AAAA,iBACzC,OAAO;AACfL,wBAAA,gEAAa,aAAa,KAAK;AAAA,QAChC;AACK,aAAA,aAAa,OAAO,SAAS;AAAA,MACnC;AAGAK,kDAAmB,WAAW,SAAS;AAClC,WAAA,aAAa,IAAI,SAAS;AAE/B,WAAK,YAAY;AAEb,UAAA;AAEG,cAAA,cAAc,KAAK,yBAAyB;AAClD,cAAM,WAAW,GAAG,KAAK,SAAS,YAAY,IAAI,KAAK,WAAW,IAAI,WAAW,QAAQ,KAAK,WAAW,IAAI;AACvG,cAAA,YAAY,MAAMD,gBAAAA,iBAAiB;AAAA,UACxC;AAAA,UACA;AAAA,UACA,MAAM,KAAK,iBAAiB;AAAA,UAC5B,EAAE,cAAc,KAAK,IAAI;AAAA;AAAA,QAAA;AAG1B,YAAI,UAAU,SAAS;AACjB,eAAA,YAAY,UAAU,QAAQ,CAAA;AAGnC,cAAI,UAAU,YAAY;AACzB,iBAAK,aAAa;AAAA,cACjB,GAAG,KAAK;AAAA,cACR,GAAG,UAAU;AAAA,YAAA;AAAA,UAEf;AAEAJ,8BAAY,MAAA,OAAA,4CAAA,cAAc,KAAK,WAAW,IAAI,MAAM,KAAK,UAAU,MAAM,IAAI,KAAK,WAAW,KAAK,MAAM;AAGxG,gBAAM,eAAeK,yBAAA,mBAAmB,SAAS,WAAW,UAAU;AACjE,eAAA,aAAa,OAAO,SAAS;AAClCA,mCAAA,mBAAmB,sBAAsB,aAAa,KAAK,UAAU,QAAQ,YAAY;AAGtEA,mCAAAA,mBAAA,eAAe,QAAQ,CAAC,YAAY;AAAA,QAAA,OACjD;AACNL,wBAAA,iEAAc,aAAa,UAAU,OAAO;AAC5C,eAAK,YAAY;AACjB,eAAK,WAAW,QAAQ;AACxB,eAAK,WAAW,aAAa;AACVK,mCAAAA,mBAAA,SAAS,WAAW,UAAU;AAC5C,eAAA,aAAa,OAAO,SAAS;AAAA,QACnC;AAAA,eACQ,OAAO;AACfL,sBAAA,MAAA,MAAA,SAAA,4CAAc,aAAa,KAAK;AAChC,aAAK,YAAY;AACjB,aAAK,WAAW,QAAQ;AACxB,aAAK,WAAW,aAAa;AAC7BC,sBAAA,UAAU,UAAU;AACDI,iCAAAA,mBAAA,SAAS,WAAW,UAAU;AAC5C,aAAA,aAAa,OAAO,SAAS;AAAA,MAAA,UACjC;AACD,aAAK,YAAY;AAAA,MAClB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,mBAAmB;AACxB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEnC,YAAA,cAAc,QAAQ,KAAK,WAAW,IAAI,UAAU,KAAK,WAAW,KAAK;AAC7E,YAAI,KAAK,yBAAyB,KAAK,0BAA0B,OAAO;AACvE,yBAAe,oBAAoB,mBAAmB,KAAK,qBAAqB,CAAC;AAAA,QAClF;AAEM,cAAA,gBAAgBP,8BAAY,kBAAkB;AAAA,UACnD,KAAKC,gBAAU,UAAA,UAAU,qBAAqB,mBAAmB,KAAK,WAAW,CAAC,IAAI,WAAW,EAAE;AAAA,UACnG,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACb,gBAAA,IAAI,eAAe,KAAK;AAC3B,sBAAQ,IAAI,IAAI;AAAA,YAAA,OACV;AACN,qBAAO,IAAI,MAAM,UAAU,IAAI,UAAU,GAAG,CAAC;AAAA,YAC9C;AAAA,UACD;AAAA,UACA,MAAM,CAAC,QAAQ;AACdC,0BAAA,MAAA,MAAA,SAAA,4CAAc,eAAe,GAAG;AACzB,mBAAA,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QAAA,CACA;AAEDA,4BAAI,QAAQ,aAAa;AAAA,MAAA,CACzB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,gBAAgB;AACrB,WAAK,eAAe;AAChB,UAAA;AAEG,cAAA,KAAK,WAAW,IAAI;AAAA,eAClB,OAAO;AACfA,sBAAA,iEAAc,SAAS,KAAK;AAAA,MAAA,UAC3B;AACD,aAAK,eAAe;AAAA,MACrB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,YAAY,WAAW,SAAS;AACxB,aAAAD,0BAAU,YAAY,WAAW,OAAO;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA,IAOA,iBAAiB,GAAG;;AACnBC,oBAAA,MAAc,MAAA,SAAA,4CAAA,WAAW,CAAC;AAC1BA,0BAAc,MAAA,SAAA,4CAAA,eAAa,OAAE,WAAF,mBAAU,QAAO,IAAI;AAChDA,oBAAA,MAAA,MAAA,SAAA,4CAAc,SAAS,EAAE,MAAM;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA,IAKA,WAAW,SAAS;AACnB,UAAI,CAAC;AAAgB,eAAA;AAEjB,UAAA;AACG,cAAA,OAAO,IAAI,KAAK,OAAO;AAC7B,eAAO,KAAK;eACJ,OAAO;AACR,eAAA;AAAA,MACR;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,wBAAwB;AACjB,YAAA,SAASK,4CAAmB;AACtBL,oBAAAA,MAAA,MAAA,OAAA,4CAAA,MAAM;AAG0B;AAC3CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QAAA,CACZ;AAAA,MACF;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,oBAAoB,MAAM;AACrB,UAAA,KAAK,gBAAgB,KAAK,aAAa;qFAC9B,iBAAiB;AAE7B,cAAM,WAAW,GAAG,KAAK,SAAS,YAAY,IAAI,KAAK,WAAW;AACjDI,wBAAAA,iBAAA,YAAY,UAAU,QAAQ;AAC/C,aAAK,WAAW,IAAI;AAAA,MACrB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,sBAAsB;AACrBJ,mFAAY,eAAe,KAAK,aAAa,IAAI;AACtC,iBAAA,aAAa,KAAK,cAAc;AACtC,YAAA;AACgBK,mCAAAA,mBAAA,SAAS,WAAW,UAAU;AACjDL,wBAAA,MAAY,MAAA,OAAA,4CAAA,WAAW,SAAS;AAAA,iBACxB,OAAO;AACfA,wBAAA,MAAa,MAAA,QAAA,4CAAA,YAAY,WAAW,KAAK;AAAA,QAC1C;AAAA,MACD;AACA,WAAK,aAAa;IACnB;AAAA;AAAA;AAAA;AAAA,IAKA,mBAAmB;AAClB,WAAK,cAAc;AAEnB,WAAK,cAAc;AAAA,QAClB,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,cAAc;AAAA,MAAA;AAAA,IAEhB;AAAA;AAAA;AAAA;AAAA,IAKA,oBAAoB;AACnB,WAAK,cAAc;AACnB,WAAK,eAAe;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,gBAAgB;;AACrB,WAAK,eAAe;AAEhB,UAAA;AACG,cAAA,gBAAgBF,8BAAY,kBAAkB;AAAA,UACnD,KAAKC,gBAAAA,UAAU,UAAU,+BAA+B;AAAA,UACxD,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,aAAa,KAAK;AAAA,YAClB,gBAAgB,KAAK,0BAA0B,QAC7C,KAAK,gBAAgB,CAAC,KAAK,YAAa,KAAK;AAAA,YAC/C,QAAQ,KAAK,YAAY,SAAS,WAAW,KAAK,YAAY,MAAM,IAAI;AAAA,YACxE,cAAc,KAAK,YAAY,eAAe,WAAW,KAAK,YAAY,YAAY,IAAI;AAAA,YAC1F,cAAc,KAAK,YAAY,eAAe,WAAW,KAAK,YAAY,YAAY,IAAI;AAAA,UAC3F;AAAA,UACA,SAAS;AAAA,QAAA,CACT;AAED,cAAM,WAAW,MAAMC,cAAAA,MAAI,QAAQ,aAAa;AAEhD,YAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACzDA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACN;AAGD,eAAK,kBAAkB;AAGvB,gBAAM,KAAK;QAAoB,OACzB;AACN,gBAAM,IAAI,QAAM,cAAS,SAAT,mBAAe,YAAW,MAAM;AAAA,QACjD;AAAA,eACQ,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,6CAAc,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QAAA,CACN;AAAA,MAAA,UACA;AACD,aAAK,eAAe;AAAA,MACrB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM,OAAO;AAER,UAAAF,kBAAAA,YAAY,gBAAgB;AAC1B,aAAA,WAAWA,8BAAY;AAE5B,cAAM,QAAQ,IAAI;AAAA,UACjB,KAAK,oBAAoB;AAAA,UACzB,KAAK,oBAAoB;AAAA,QAAA,CACzB;AAED,cAAM,KAAK;MACZ;AAAA,IAED;AAAA,EACD;AAAA,EAEA,OAAO,SAAS;AACV,SAAA,cAAc,QAAQ,eAAe;AACtC,QAAA,CAAC,KAAK,aAAa;AACtBG,oBAAA,UAAU,SAAS;AACnBD,oBAAA,MAAI,aAAa;AACjB;AAAA,IACD;AACA,SAAK,KAAK;AAGNA,kBAAAA,MAAA,IAAI,iBAAiB,KAAK,mBAAmB;AAAA,EAClD;AAAA,EAEA,SAAS;AAER,QAAI,KAAK,eAAe,KAAK,SAAS,cAAc;AAEnD,eAAS,OAAO,GAAG,QAAQ,IAAI,QAAQ;AAChC,cAAA,WAAW,GAAG,KAAK,SAAS,YAAY,IAAI,KAAK,WAAW,QAAQ,IAAI;AAC7DI,wBAAAA,iBAAA,YAAY,UAAU,QAAQ;AAAA,MAChD;AAGA,WAAK,WAAW,OAAO;AACvB,WAAK,WAAW,IAAI;AAAA,IACrB;AAAA,EACD;AAAA,EAEA,cAAc;AAEb,QAAI,KAAK,cAAc;AACtB,WAAK,eAAe;AACpB,WAAK,iBAAiB;AACf,aAAA;AAAA,IACR;AAGA,SAAK,oBAAoB;AAElB,WAAA;AAAA,EACR;AAAA,EAEA,WAAW;AAENJ,kBAAAA,MAAA,KAAK,iBAAiB,KAAK,mBAAmB;AAGlD,SAAK,oBAAoB;AAAA,EAC1B;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7kCD,GAAG,WAAW,eAAe;"}