/**
 * 订阅消息服务类
 * 处理用户订阅管理和消息发送逻辑
 */

const WechatService = require('./wechatService');
const { query } = require('../config/database');

class SubscriptionService {
    constructor() {
        this.wechatService = new WechatService();
    }

    /**
     * 保存用户订阅状态
     * @param {string} openid 用户openid
     * @param {string} templateId 模板ID
     * @param {Object} userInfo 用户信息
     */
    async saveUserSubscription(openid, templateId, userInfo) {
        try {
            // 检查是否已存在订阅记录
            const checkSql = `
                SELECT id FROM user_subscriptions 
                WHERE openid = ? AND template_id = ?
            `;
            
            const existing = await query(checkSql, [openid, templateId]);
            
            if (existing.length > 0) {
                // 更新现有记录
                const updateSql = `
                    UPDATE user_subscriptions 
                    SET status = 1, subscribe_time = NOW(), username = ?, factory_name = ?
                    WHERE openid = ? AND template_id = ?
                `;
                
                await query(updateSql, [
                    userInfo.username || null,
                    userInfo.factory_name || null,
                    openid,
                    templateId
                ]);
                
                console.log('✅ 更新用户订阅状态成功');
            } else {
                // 插入新记录
                const insertSql = `
                    INSERT INTO user_subscriptions (openid, template_id, username, factory_name, subscribe_time, status)
                    VALUES (?, ?, ?, ?, NOW(), 1)
                `;
                
                await query(insertSql, [
                    openid,
                    templateId,
                    userInfo.username || null,
                    userInfo.factory_name || null
                ]);
                
                console.log('✅ 保存用户订阅状态成功');
            }
        } catch (error) {
            console.error('❌ 保存用户订阅状态失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取用户订阅信息
     * @param {string} username 用户名
     * @param {string} templateId 模板ID
     * @returns {Promise<Object|null>} 订阅信息
     */
    async getUserSubscription(username, templateId) {
        try {
            const sql = `
                SELECT openid, template_id, username, factory_name, status
                FROM user_subscriptions
                WHERE username = ? AND template_id = ? AND status = 1
                ORDER BY subscribe_time DESC
                LIMIT 1
            `;
            
            const result = await query(sql, [username, templateId]);
            return result.length > 0 ? result[0] : null;
        } catch (error) {
            console.error('❌ 获取用户订阅信息失败:', error.message);
            return null;
        }
    }

    /**
     * 检查订单是否已发送过订阅消息
     * @param {string} orderNo 订单号
     * @param {string} templateId 模板ID
     * @returns {Promise<boolean>} 是否已发送
     */
    async hasOrderNotificationSent(orderNo, templateId) {
        try {
            const sql = `
                SELECT id FROM message_logs
                WHERE template_id = ? AND message_content LIKE ? AND send_status = 1
                LIMIT 1
            `;
            
            const searchPattern = `%"character_string1":{"value":"${orderNo}"}%`;
            const result = await query(sql, [templateId, searchPattern]);
            
            return result.length > 0;
        } catch (error) {
            console.error('❌ 检查订单通知状态失败:', error.message);
            return false;
        }
    }

    /**
     * 发送订单图片上传通知
     * @param {string} orderNo 订单号
     * @param {string} username 用户名
     */
    async sendOrderImageUploadNotification(orderNo, username) {
        try {
            console.log(`🔔 准备发送订单图片上传通知: ${orderNo} -> ${username}`);
            
            // 检查是否已发送过该订单的通知
            const alreadySent = await this.hasOrderNotificationSent(orderNo, this.wechatService.templateId);
            if (alreadySent) {
                console.log(`⚠️ 订单 ${orderNo} 已发送过通知，跳过发送`);
                return { success: true, message: '该订单已发送过通知' };
            }

            // 获取用户订阅信息
            const userSubscription = await this.getUserSubscription(username, this.wechatService.templateId);
            if (!userSubscription) {
                console.log(`⚠️ 用户 ${username} 未订阅消息通知`);
                return { success: false, message: '用户未订阅消息通知' };
            }

            // 获取订单详细信息
            const orderInfo = await this.getOrderInfo(orderNo, userSubscription.factory_name);
            if (!orderInfo) {
                console.log(`⚠️ 未找到订单信息: ${orderNo}`);
                return { success: false, message: '未找到订单信息' };
            }

            // 构建消息数据
            const messageData = {
                character_string1: { value: orderInfo.order_no }, // 订单号
                thing2: { value: orderInfo.receiver || '客户' }, // 客户名称
                thing11: { value: orderInfo.follower || '处理人' }, // 当前处理人
                phrase13: { value: '坯布商已发货' }, // 订单状态
                time31: { value: this.formatDateTime(orderInfo.created_at) } // 下单时间
            };

            // 发送订阅消息
            const result = await this.wechatService.sendSubscribeMessage(
                userSubscription.openid,
                messageData,
                `pages/imageManage/imageManage?orderNumber=${encodeURIComponent(orderNo)}`
            );

            if (result.success) {
                console.log(`✅ 订单图片上传通知发送成功: ${orderNo}`);
                return { success: true, message: '通知发送成功' };
            } else {
                console.error(`❌ 订单图片上传通知发送失败: ${orderNo}`, result.error);
                return { success: false, message: '通知发送失败', error: result.error };
            }

        } catch (error) {
            console.error('❌ 发送订单图片上传通知异常:', error.message);
            return { success: false, message: '发送通知异常', error: error.message };
        }
    }

    /**
     * 获取订单信息
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     * @returns {Promise<Object|null>} 订单信息
     */
    async getOrderInfo(orderNo, factoryName) {
        try {
            const sql = `
                SELECT order_no, receiver, follower, created_at
                FROM shipping_detail
                WHERE order_no = ? AND receiver = ?
                ORDER BY created_at DESC
                LIMIT 1
            `;
            
            const result = await query(sql, [orderNo, factoryName]);
            return result.length > 0 ? result[0] : null;
        } catch (error) {
            console.error('❌ 获取订单信息失败:', error.message);
            return null;
        }
    }

    /**
     * 格式化日期时间
     * @param {Date|string} dateTime 日期时间
     * @returns {string} 格式化后的日期时间
     */
    formatDateTime(dateTime) {
        if (!dateTime) return new Date().toLocaleString('zh-CN');
        
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

module.exports = SubscriptionService;
