<view class="container data-v-9d986af9"><view class="header data-v-9d986af9"><text class="title data-v-9d986af9">订阅消息测试</text></view><view class="content data-v-9d986af9"><view class="status-card data-v-9d986af9"><view class="status-title data-v-9d986af9">订阅状态</view><view class="status-content data-v-9d986af9"><text class="{{['data-v-9d986af9', 'status-text', b]}}">{{a}}</text><button wx:if="{{c}}" bindtap="{{d}}" class="subscribe-btn data-v-9d986af9"> 申请订阅 </button></view></view><view class="test-card data-v-9d986af9"><view class="test-title data-v-9d986af9">测试功能</view><view class="test-content data-v-9d986af9"><view class="input-group data-v-9d986af9"><text class="input-label data-v-9d986af9">测试订单号:</text><input placeholder="请输入订单号" class="input-field data-v-9d986af9" value="{{e}}" bindinput="{{f}}"/></view><button bindtap="{{g}}" disabled="{{h}}" class="test-btn data-v-9d986af9"> 发送测试通知 </button></view></view><view class="history-card data-v-9d986af9"><view class="history-title data-v-9d986af9">消息历史</view><view class="history-content data-v-9d986af9"><button bindtap="{{i}}" class="history-btn data-v-9d986af9">刷新历史</button><view wx:if="{{j}}" class="history-list data-v-9d986af9"><view wx:for="{{k}}" wx:for-item="message" wx:key="f" class="history-item data-v-9d986af9"><view class="message-status data-v-9d986af9"><text class="{{['data-v-9d986af9', 'status-dot', message.a]}}"></text><text class="status-label data-v-9d986af9">{{message.b}}</text></view><view class="message-time data-v-9d986af9">{{message.c}}</view><view wx:if="{{message.d}}" class="error-msg data-v-9d986af9">{{message.e}}</view></view></view><view wx:else class="no-history data-v-9d986af9"><text class="data-v-9d986af9">暂无消息历史</text></view></view></view><view class="debug-card data-v-9d986af9"><view class="debug-title data-v-9d986af9">调试功能</view><view class="debug-content data-v-9d986af9"><button bindtap="{{l}}" class="debug-btn data-v-9d986af9">清除OpenID缓存</button><button bindtap="{{m}}" class="debug-btn data-v-9d986af9">检查订阅状态</button></view></view></view></view>