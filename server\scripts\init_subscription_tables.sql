-- 微信小程序订阅消息相关表初始化脚本
-- 数据库: identify

USE identify;

-- 创建用户订阅表
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    openid VARCHAR(100) NOT NULL COMMENT '用户openid',
    template_id VARCHAR(100) NOT NULL COMMENT '模板ID',
    username VARCHAR(50) COMMENT '用户名',
    factory_name VARCHAR(100) COMMENT '工厂名称',
    subscribe_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '订阅时间',
    status TINYINT DEFAULT 1 COMMENT '订阅状态：1=已订阅，0=已取消',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_openid (openid),
    INDEX idx_template_id (template_id),
    INDEX idx_username (username),
    INDEX idx_status (status),
    UNIQUE KEY uk_openid_template (openid, template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户订阅表';

-- 创建消息发送记录表
CREATE TABLE IF NOT EXISTS message_logs (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    openid VARCHAR(100) NOT NULL COMMENT '用户openid',
    template_id VARCHAR(100) NOT NULL COMMENT '模板ID',
    message_content TEXT COMMENT '消息内容JSON',
    send_status TINYINT DEFAULT 0 COMMENT '发送状态：0=发送中，1=成功，2=失败',
    error_msg VARCHAR(500) COMMENT '错误信息',
    send_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_openid (openid),
    INDEX idx_template_id (template_id),
    INDEX idx_send_status (send_status),
    INDEX idx_send_time (send_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息发送记录表';

-- 插入测试数据（可选）
-- INSERT INTO user_subscriptions (openid, template_id, username, factory_name, status) 
-- VALUES ('test_openid_123', 'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8', 'test_user', '测试工厂', 1);

-- 查看表结构
DESCRIBE user_subscriptions;
DESCRIBE message_logs;

-- 查看表数据
SELECT COUNT(*) as user_subscriptions_count FROM user_subscriptions;
SELECT COUNT(*) as message_logs_count FROM message_logs;
