<template>
	<view class="container">
		<view class="header">
			<text class="title">订阅消息测试</text>
		</view>
		
		<view class="content">
			<!-- 订阅状态 -->
			<view class="status-card">
				<view class="status-title">订阅状态</view>
				<view class="status-content">
					<text :class="['status-text', subscribed ? 'subscribed' : 'not-subscribed']">
						{{ subscribed ? '已订阅' : '未订阅' }}
					</text>
					<button v-if="!subscribed" @click="requestSubscribe" class="subscribe-btn">
						申请订阅
					</button>
				</view>
			</view>
			
			<!-- 测试功能 -->
			<view class="test-card">
				<view class="test-title">测试功能</view>
				<view class="test-content">
					<view class="input-group">
						<text class="input-label">测试订单号:</text>
						<input v-model="testOrderNo" placeholder="请输入订单号" class="input-field" />
					</view>
					<button @click="sendTestNotification" :disabled="!testOrderNo" class="test-btn">
						发送测试通知
					</button>
				</view>
			</view>
			
			<!-- 消息历史 -->
			<view class="history-card">
				<view class="history-title">消息历史</view>
				<view class="history-content">
					<button @click="loadMessageHistory" class="history-btn">刷新历史</button>
					<view v-if="messageHistory.length > 0" class="history-list">
						<view v-for="(message, index) in messageHistory" :key="index" class="history-item">
							<view class="message-status">
								<text :class="['status-dot', message.send_status === 1 ? 'success' : 'failed']"></text>
								<text class="status-label">{{ message.send_status === 1 ? '发送成功' : '发送失败' }}</text>
							</view>
							<view class="message-time">{{ formatTime(message.send_time) }}</view>
							<view v-if="message.error_msg" class="error-msg">{{ message.error_msg }}</view>
						</view>
					</view>
					<view v-else class="no-history">
						<text>暂无消息历史</text>
					</view>
				</view>
			</view>
			
			<!-- 调试功能 -->
			<view class="debug-card">
				<view class="debug-title">调试功能</view>
				<view class="debug-content">
					<button @click="clearOpenidCache" class="debug-btn">清除OpenID缓存</button>
					<button @click="checkSubscriptionStatus" class="debug-btn">检查订阅状态</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import subscriptionManager from '../../utils/subscriptionManager.js';
	import { showSuccess, showError } from '../../utils/helpers.js';

	export default {
		data() {
			return {
				subscribed: false,
				testOrderNo: '',
				messageHistory: []
			}
		},
		
		onLoad() {
			this.checkSubscriptionStatus();
			this.loadMessageHistory();
		},
		
		methods: {
			/**
			 * 申请订阅消息
			 */
			async requestSubscribe() {
				try {
					uni.showLoading({ title: '申请中...' });
					const success = await subscriptionManager.requestSubscribeMessage();
					if (success) {
						this.subscribed = true;
						showSuccess('订阅成功');
					}
				} catch (error) {
					console.error('申请订阅失败:', error);
					showError('申请订阅失败');
				} finally {
					uni.hideLoading();
				}
			},
			
			/**
			 * 检查订阅状态
			 */
			async checkSubscriptionStatus() {
				try {
					this.subscribed = await subscriptionManager.checkSubscriptionStatus();
				} catch (error) {
					console.error('检查订阅状态失败:', error);
				}
			},
			
			/**
			 * 发送测试通知
			 */
			async sendTestNotification() {
				if (!this.testOrderNo.trim()) {
					showError('请输入订单号');
					return;
				}
				
				try {
					uni.showLoading({ title: '发送中...' });
					const success = await subscriptionManager.sendTestNotification(this.testOrderNo.trim());
					if (success) {
						// 刷新消息历史
						setTimeout(() => {
							this.loadMessageHistory();
						}, 1000);
					}
				} catch (error) {
					console.error('发送测试通知失败:', error);
					showError('发送失败');
				} finally {
					uni.hideLoading();
				}
			},
			
			/**
			 * 加载消息历史
			 */
			async loadMessageHistory() {
				try {
					const result = await subscriptionManager.getMessageHistory(1, 10);
					if (result) {
						this.messageHistory = result.messages || [];
					}
				} catch (error) {
					console.error('加载消息历史失败:', error);
				}
			},
			
			/**
			 * 清除OpenID缓存
			 */
			clearOpenidCache() {
				subscriptionManager.clearOpenidCache();
				showSuccess('OpenID缓存已清除');
			},
			
			/**
			 * 格式化时间
			 */
			formatTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				return date.toLocaleString('zh-CN');
			}
		}
	}
</script>

<style scoped>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.status-card, .test-card, .history-card, .debug-card {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
	}
	
	.status-title, .test-title, .history-title, .debug-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.status-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.status-text {
		font-size: 28rpx;
	}
	
	.subscribed {
		color: #52c41a;
	}
	
	.not-subscribed {
		color: #ff4d4f;
	}
	
	.subscribe-btn, .test-btn, .history-btn, .debug-btn {
		background: #667eea;
		color: white;
		border: none;
		border-radius: 8rpx;
		padding: 16rpx 32rpx;
		font-size: 28rpx;
		margin: 10rpx;
	}
	
	.subscribe-btn:active, .test-btn:active, .history-btn:active, .debug-btn:active {
		background: #5a6fd8;
	}
	
	.input-group {
		margin-bottom: 20rpx;
	}
	
	.input-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.input-field {
		width: 100%;
		padding: 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 8rpx;
		font-size: 28rpx;
	}
	
	.history-list {
		margin-top: 20rpx;
	}
	
	.history-item {
		padding: 20rpx;
		border-bottom: 2rpx solid #f0f0f0;
	}
	
	.message-status {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.status-dot {
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		margin-right: 16rpx;
	}
	
	.status-dot.success {
		background: #52c41a;
	}
	
	.status-dot.failed {
		background: #ff4d4f;
	}
	
	.status-label {
		font-size: 26rpx;
		color: #333;
	}
	
	.message-time {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 10rpx;
	}
	
	.error-msg {
		font-size: 24rpx;
		color: #ff4d4f;
	}
	
	.no-history {
		text-align: center;
		color: #999;
		padding: 40rpx;
	}
	
	.debug-content {
		display: flex;
		flex-wrap: wrap;
	}
</style>
