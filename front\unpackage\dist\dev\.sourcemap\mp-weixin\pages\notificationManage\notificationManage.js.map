{"version": 3, "file": "notificationManage.js", "sources": ["pages/notificationManage/notificationManage.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90aWZpY2F0aW9uTWFuYWdlL25vdGlmaWNhdGlvbk1hbmFnZS52dWU"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"title\">通知管理</text>\n\t\t\t<button @click=\"refreshData\" class=\"refresh-btn\">刷新</button>\n\t\t</view>\n\t\t\n\t\t<!-- 统计卡片 -->\n\t\t<view class=\"stats-section\">\n\t\t\t<view class=\"stat-card\">\n\t\t\t\t<text class=\"stat-number\">{{ stats.total || 0 }}</text>\n\t\t\t\t<text class=\"stat-label\">总通知</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-card\">\n\t\t\t\t<text class=\"stat-number\">{{ stats.byStatus?.PENDING || 0 }}</text>\n\t\t\t\t<text class=\"stat-label\">待处理</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-card\">\n\t\t\t\t<text class=\"stat-number\">{{ stats.byStatus?.SENT || 0 }}</text>\n\t\t\t\t<text class=\"stat-label\">已发送</text>\n\t\t\t</view>\n\t\t\t<view class=\"stat-card\">\n\t\t\t\t<text class=\"stat-number\">{{ stats.byStatus?.FAILED || 0 }}</text>\n\t\t\t\t<text class=\"stat-label\">失败</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 筛选器 -->\n\t\t<view class=\"filter-section\">\n\t\t\t<picker @change=\"onStatusChange\" :value=\"statusIndex\" :range=\"statusOptions\">\n\t\t\t\t<view class=\"picker-item\">\n\t\t\t\t\t状态: {{ statusOptions[statusIndex] }}\n\t\t\t\t</view>\n\t\t\t</picker>\n\t\t\t\n\t\t\t<picker @change=\"onTypeChange\" :value=\"typeIndex\" :range=\"typeOptions\">\n\t\t\t\t<view class=\"picker-item\">\n\t\t\t\t\t类型: {{ typeOptions[typeIndex] }}\n\t\t\t\t</view>\n\t\t\t</picker>\n\t\t</view>\n\t\t\n\t\t<!-- 通知列表 -->\n\t\t<view class=\"notification-list\">\n\t\t\t<view v-for=\"(notification, index) in notifications\" :key=\"notification.id\" class=\"notification-item\">\n\t\t\t\t<view class=\"notification-header\">\n\t\t\t\t\t<text class=\"order-no\">{{ notification.order_no }}</text>\n\t\t\t\t\t<view :class=\"['status-badge', getStatusClass(notification.status)]\">\n\t\t\t\t\t\t{{ getStatusText(notification.status) }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"notification-content\">\n\t\t\t\t\t<text class=\"receiver\">客户: {{ notification.receiver }}</text>\n\t\t\t\t\t<text class=\"follower\">跟单: {{ notification.follower || '未指定' }}</text>\n\t\t\t\t\t<text class=\"type\">类型: {{ getTypeText(notification.notification_type) }}</text>\n\t\t\t\t\t<text class=\"time\">创建: {{ formatTime(notification.created_time) }}</text>\n\t\t\t\t\t\n\t\t\t\t\t<view v-if=\"notification.processed_at\" class=\"processed-time\">\n\t\t\t\t\t\t<text>处理: {{ formatTime(notification.processed_at) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view v-if=\"notification.retry_count > 0\" class=\"retry-info\">\n\t\t\t\t\t\t<text>重试次数: {{ notification.retry_count }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view v-if=\"notification.error_message\" class=\"error-message\">\n\t\t\t\t\t\t<text>错误: {{ notification.error_message }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"notification-actions\">\n\t\t\t\t\t<button v-if=\"notification.status === 'FAILED'\" \n\t\t\t\t\t\t\t@click=\"retryNotification(notification.id)\" \n\t\t\t\t\t\t\tclass=\"action-btn retry-btn\">\n\t\t\t\t\t\t重试\n\t\t\t\t\t</button>\n\t\t\t\t\t<button @click=\"deleteNotification(notification.id)\" \n\t\t\t\t\t\t\tclass=\"action-btn delete-btn\">\n\t\t\t\t\t\t删除\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view v-if=\"notifications.length === 0\" class=\"empty-state\">\n\t\t\t\t<text>暂无通知记录</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 分页 -->\n\t\t<view v-if=\"pagination.totalPages > 1\" class=\"pagination\">\n\t\t\t<button @click=\"prevPage\" :disabled=\"pagination.page <= 1\" class=\"page-btn\">上一页</button>\n\t\t\t<text class=\"page-info\">{{ pagination.page }} / {{ pagination.totalPages }}</text>\n\t\t\t<button @click=\"nextPage\" :disabled=\"pagination.page >= pagination.totalPages\" class=\"page-btn\">下一页</button>\n\t\t</view>\n\t\t\n\t\t<!-- 操作按钮 -->\n\t\t<view class=\"action-section\">\n\t\t\t<button @click=\"showTestModal = true\" class=\"test-btn\">测试通知</button>\n\t\t\t<button @click=\"cleanupNotifications\" class=\"cleanup-btn\">清理已发送</button>\n\t\t</view>\n\t\t\n\t\t<!-- 测试通知弹窗 -->\n\t\t<view v-if=\"showTestModal\" class=\"modal-overlay\" @click=\"showTestModal = false\">\n\t\t\t<view class=\"modal-content\" @click.stop>\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">测试订单生成通知</text>\n\t\t\t\t\t<text @click=\"showTestModal = false\" class=\"modal-close\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-body\">\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<text class=\"input-label\">订单号:</text>\n\t\t\t\t\t\t<input v-model=\"testForm.order_no\" placeholder=\"请输入测试订单号\" class=\"input-field\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<text class=\"input-label\">接收方:</text>\n\t\t\t\t\t\t<input v-model=\"testForm.receiver\" placeholder=\"请输入工厂名称\" class=\"input-field\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<text class=\"input-label\">跟单员:</text>\n\t\t\t\t\t\t<input v-model=\"testForm.follower\" placeholder=\"请输入跟单员\" class=\"input-field\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button @click=\"showTestModal = false\" class=\"cancel-btn\">取消</button>\n\t\t\t\t\t<button @click=\"triggerTestNotification\" class=\"confirm-btn\">发送</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport apiService from '../../utils/apiService.js';\n\timport { showSuccess, showError } from '../../utils/helpers.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tstats: {},\n\t\t\t\tnotifications: [],\n\t\t\t\tpagination: {\n\t\t\t\t\tpage: 1,\n\t\t\t\t\tlimit: 20,\n\t\t\t\t\ttotal: 0,\n\t\t\t\t\ttotalPages: 0\n\t\t\t\t},\n\t\t\t\tstatusIndex: 0,\n\t\t\t\tstatusOptions: ['全部', 'PENDING', 'PROCESSING', 'SENT', 'FAILED'],\n\t\t\t\ttypeIndex: 0,\n\t\t\t\ttypeOptions: ['全部', 'ORDER_GENERATED', 'IMAGE_UPLOADED'],\n\t\t\t\tshowTestModal: false,\n\t\t\t\ttestForm: {\n\t\t\t\t\torder_no: '',\n\t\t\t\t\treceiver: '',\n\t\t\t\t\tfollower: ''\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\tonLoad() {\n\t\t\tthis.loadNotificationStats();\n\t\t\tthis.loadNotifications();\n\t\t},\n\t\t\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 加载通知统计\n\t\t\t */\n\t\t\tasync loadNotificationStats() {\n\t\t\t\ttry {\n\t\t\t\t\tconst response = await apiService.get('/notifications/status');\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tthis.stats = response.data.queue;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载通知统计失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 加载通知列表\n\t\t\t */\n\t\t\tasync loadNotifications() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '加载中...' });\n\t\t\t\t\t\n\t\t\t\t\tconst params = new URLSearchParams({\n\t\t\t\t\t\tpage: this.pagination.page,\n\t\t\t\t\t\tlimit: this.pagination.limit\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (this.statusIndex > 0) {\n\t\t\t\t\t\tparams.append('status', this.statusOptions[this.statusIndex]);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (this.typeIndex > 0) {\n\t\t\t\t\t\tparams.append('notification_type', this.typeOptions[this.typeIndex]);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst response = await apiService.get(`/notifications/queue?${params.toString()}`);\n\t\t\t\t\t\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tthis.notifications = response.data.notifications;\n\t\t\t\t\t\tthis.pagination = response.data.pagination;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tshowError(response.message || '加载失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载通知列表失败:', error);\n\t\t\t\t\tshowError('加载失败');\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 刷新数据\n\t\t\t */\n\t\t\tasync refreshData() {\n\t\t\t\tawait this.loadNotificationStats();\n\t\t\t\tawait this.loadNotifications();\n\t\t\t\tshowSuccess('刷新成功');\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 状态筛选变更\n\t\t\t */\n\t\t\tonStatusChange(e) {\n\t\t\t\tthis.statusIndex = e.detail.value;\n\t\t\t\tthis.pagination.page = 1;\n\t\t\t\tthis.loadNotifications();\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 类型筛选变更\n\t\t\t */\n\t\t\tonTypeChange(e) {\n\t\t\t\tthis.typeIndex = e.detail.value;\n\t\t\t\tthis.pagination.page = 1;\n\t\t\t\tthis.loadNotifications();\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 上一页\n\t\t\t */\n\t\t\tprevPage() {\n\t\t\t\tif (this.pagination.page > 1) {\n\t\t\t\t\tthis.pagination.page--;\n\t\t\t\t\tthis.loadNotifications();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 下一页\n\t\t\t */\n\t\t\tnextPage() {\n\t\t\t\tif (this.pagination.page < this.pagination.totalPages) {\n\t\t\t\t\tthis.pagination.page++;\n\t\t\t\t\tthis.loadNotifications();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 重试通知\n\t\t\t */\n\t\t\tasync retryNotification(id) {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '重试中...' });\n\t\t\t\t\tconst response = await apiService.post(`/notifications/retry/${id}`);\n\t\t\t\t\t\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tshowSuccess('重试成功');\n\t\t\t\t\t\tthis.loadNotifications();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tshowError(response.message || '重试失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('重试通知失败:', error);\n\t\t\t\t\tshowError('重试失败');\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 删除通知\n\t\t\t */\n\t\t\tasync deleteNotification(id) {\n\t\t\t\ttry {\n\t\t\t\t\tconst confirmed = await new Promise((resolve) => {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\t\t\tcontent: '确定要删除这条通知吗？',\n\t\t\t\t\t\t\tsuccess: (res) => resolve(res.confirm)\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (!confirmed) return;\n\t\t\t\t\t\n\t\t\t\t\tuni.showLoading({ title: '删除中...' });\n\t\t\t\t\tconst response = await apiService.delete(`/notifications/${id}`);\n\t\t\t\t\t\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tshowSuccess('删除成功');\n\t\t\t\t\t\tthis.loadNotifications();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tshowError(response.message || '删除失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('删除通知失败:', error);\n\t\t\t\t\tshowError('删除失败');\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 清理已发送通知\n\t\t\t */\n\t\t\tasync cleanupNotifications() {\n\t\t\t\ttry {\n\t\t\t\t\tconst confirmed = await new Promise((resolve) => {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '确认清理',\n\t\t\t\t\t\t\tcontent: '确定要清理7天前的已发送通知吗？',\n\t\t\t\t\t\t\tsuccess: (res) => resolve(res.confirm)\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tif (!confirmed) return;\n\t\t\t\t\t\n\t\t\t\t\tuni.showLoading({ title: '清理中...' });\n\t\t\t\t\tconst response = await apiService.post('/notifications/cleanup', { days: 7 });\n\t\t\t\t\t\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tshowSuccess(`清理完成，删除了 ${response.data.deletedCount} 条记录`);\n\t\t\t\t\t\tthis.refreshData();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tshowError(response.message || '清理失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('清理通知失败:', error);\n\t\t\t\t\tshowError('清理失败');\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 触发测试通知\n\t\t\t */\n\t\t\tasync triggerTestNotification() {\n\t\t\t\tif (!this.testForm.order_no || !this.testForm.receiver) {\n\t\t\t\t\tshowError('请填写订单号和接收方');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '发送中...' });\n\t\t\t\t\tconst response = await apiService.post('/notifications/trigger-order-notification', this.testForm);\n\t\t\t\t\t\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tshowSuccess('测试通知已发送');\n\t\t\t\t\t\tthis.showTestModal = false;\n\t\t\t\t\t\tthis.testForm = { order_no: '', receiver: '', follower: '' };\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.refreshData();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tshowError(response.message || '发送失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('发送测试通知失败:', error);\n\t\t\t\t\tshowError('发送失败');\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 获取状态样式类\n\t\t\t */\n\t\t\tgetStatusClass(status) {\n\t\t\t\tconst classMap = {\n\t\t\t\t\t'PENDING': 'status-pending',\n\t\t\t\t\t'PROCESSING': 'status-processing',\n\t\t\t\t\t'SENT': 'status-sent',\n\t\t\t\t\t'FAILED': 'status-failed'\n\t\t\t\t};\n\t\t\t\treturn classMap[status] || 'status-unknown';\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 获取状态文本\n\t\t\t */\n\t\t\tgetStatusText(status) {\n\t\t\t\tconst textMap = {\n\t\t\t\t\t'PENDING': '待处理',\n\t\t\t\t\t'PROCESSING': '处理中',\n\t\t\t\t\t'SENT': '已发送',\n\t\t\t\t\t'FAILED': '失败'\n\t\t\t\t};\n\t\t\t\treturn textMap[status] || status;\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 获取类型文本\n\t\t\t */\n\t\t\tgetTypeText(type) {\n\t\t\t\tconst textMap = {\n\t\t\t\t\t'ORDER_GENERATED': '订单生成',\n\t\t\t\t\t'IMAGE_UPLOADED': '图片上传'\n\t\t\t\t};\n\t\t\t\treturn textMap[type] || type;\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 格式化时间\n\t\t\t */\n\t\t\tformatTime(timeStr) {\n\t\t\t\tif (!timeStr) return '';\n\t\t\t\tconst date = new Date(timeStr);\n\t\t\t\treturn date.toLocaleString('zh-CN');\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.container {\n\t\tpadding: 20rpx;\n\t\tbackground-color: #f5f5f5;\n\t\tmin-height: 100vh;\n\t}\n\t\n\t.header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.refresh-btn {\n\t\tbackground: #667eea;\n\t\tcolor: white;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 16rpx 32rpx;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.stats-section {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.stat-card {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\ttext-align: center;\n\t\tflex: 1;\n\t\tmargin: 0 10rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\n\t}\n\t\n\t.stat-number {\n\t\tdisplay: block;\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #667eea;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.stat-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.filter-section {\n\t\tdisplay: flex;\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.picker-item {\n\t\tbackground: white;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t\tmargin-right: 20rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.notification-list {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.notification-item {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\n\t}\n\t\n\t.notification-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.order-no {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.status-badge {\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: white;\n\t}\n\t\n\t.status-pending { background: #ffa500; }\n\t.status-processing { background: #1890ff; }\n\t.status-sent { background: #52c41a; }\n\t.status-failed { background: #ff4d4f; }\n\t\n\t.notification-content text {\n\t\tdisplay: block;\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.error-message text {\n\t\tcolor: #ff4d4f;\n\t\tfont-size: 24rpx;\n\t}\n\t\n\t.notification-actions {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tmargin-top: 20rpx;\n\t}\n\t\n\t.action-btn {\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 12rpx 24rpx;\n\t\tfont-size: 24rpx;\n\t\tmargin-left: 16rpx;\n\t}\n\t\n\t.retry-btn {\n\t\tbackground: #1890ff;\n\t\tcolor: white;\n\t}\n\t\n\t.delete-btn {\n\t\tbackground: #ff4d4f;\n\t\tcolor: white;\n\t}\n\t\n\t.pagination {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.page-btn {\n\t\tbackground: #667eea;\n\t\tcolor: white;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 16rpx 32rpx;\n\t\tfont-size: 28rpx;\n\t\tmargin: 0 20rpx;\n\t}\n\t\n\t.page-btn:disabled {\n\t\tbackground: #ccc;\n\t}\n\t\n\t.page-info {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.action-section {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\t\n\t.test-btn, .cleanup-btn {\n\t\tbackground: #52c41a;\n\t\tcolor: white;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx 40rpx;\n\t\tfont-size: 28rpx;\n\t\tmargin: 0 20rpx;\n\t}\n\t\n\t.cleanup-btn {\n\t\tbackground: #fa8c16;\n\t}\n\t\n\t.empty-state {\n\t\ttext-align: center;\n\t\tpadding: 80rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t/* 弹窗样式 */\n\t.modal-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0,0,0,0.5);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: 1000;\n\t}\n\t\n\t.modal-content {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\twidth: 80%;\n\t\tmax-width: 600rpx;\n\t}\n\t\n\t.modal-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 2rpx solid #f0f0f0;\n\t}\n\t\n\t.modal-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.modal-close {\n\t\tfont-size: 48rpx;\n\t\tcolor: #999;\n\t\tcursor: pointer;\n\t}\n\t\n\t.modal-body {\n\t\tpadding: 30rpx;\n\t}\n\t\n\t.input-group {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.input-label {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 16rpx;\n\t}\n\t\n\t.input-field {\n\t\twidth: 100%;\n\t\tpadding: 20rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.modal-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tpadding: 30rpx;\n\t\tborder-top: 2rpx solid #f0f0f0;\n\t}\n\t\n\t.cancel-btn, .confirm-btn {\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 16rpx 32rpx;\n\t\tfont-size: 28rpx;\n\t\tmargin-left: 20rpx;\n\t}\n\t\n\t.cancel-btn {\n\t\tbackground: #f0f0f0;\n\t\tcolor: #666;\n\t}\n\t\n\t.confirm-btn {\n\t\tbackground: #667eea;\n\t\tcolor: white;\n\t}\n</style>\n", "import MiniProgramPage from 'D:/Desktop/Warehouse/front/pages/notificationManage/notificationManage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["apiService", "uni", "showError", "showSuccess"], "mappings": ";;;;AAwIC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,OAAO,CAAE;AAAA,MACT,eAAe,CAAE;AAAA,MACjB,YAAY;AAAA,QACX,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY;AAAA,MACZ;AAAA,MACD,aAAa;AAAA,MACb,eAAe,CAAC,MAAM,WAAW,cAAc,QAAQ,QAAQ;AAAA,MAC/D,WAAW;AAAA,MACX,aAAa,CAAC,MAAM,mBAAmB,gBAAgB;AAAA,MACvD,eAAe;AAAA,MACf,UAAU;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,IACD;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,sBAAqB;AAC1B,SAAK,kBAAiB;AAAA,EACtB;AAAA,EAED,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,MAAM,wBAAwB;AAC7B,UAAI;AACH,cAAM,WAAW,MAAMA,iBAAAA,WAAW,IAAI,uBAAuB;AAC7D,YAAI,SAAS,SAAS;AACrB,eAAK,QAAQ,SAAS,KAAK;AAAA,QAC5B;AAAA,MACC,SAAO,OAAO;AACfC,sBAAA,MAAA,MAAA,SAAA,0DAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,oBAAoB;AACzB,UAAI;AACHA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,cAAM,SAAS,IAAI,gBAAgB;AAAA,UAClC,MAAM,KAAK,WAAW;AAAA,UACtB,OAAO,KAAK,WAAW;AAAA,QACxB,CAAC;AAED,YAAI,KAAK,cAAc,GAAG;AACzB,iBAAO,OAAO,UAAU,KAAK,cAAc,KAAK,WAAW,CAAC;AAAA,QAC7D;AAEA,YAAI,KAAK,YAAY,GAAG;AACvB,iBAAO,OAAO,qBAAqB,KAAK,YAAY,KAAK,SAAS,CAAC;AAAA,QACpE;AAEA,cAAM,WAAW,MAAMD,iBAAU,WAAC,IAAI,wBAAwB,OAAO,UAAU,EAAE;AAEjF,YAAI,SAAS,SAAS;AACrB,eAAK,gBAAgB,SAAS,KAAK;AACnC,eAAK,aAAa,SAAS,KAAK;AAAA,eAC1B;AACNE,wBAAAA,UAAU,SAAS,WAAW,MAAM;AAAA,QACrC;AAAA,MACC,SAAO,OAAO;AACfD,sBAAA,MAAA,MAAA,SAAA,0DAAc,aAAa,KAAK;AAChCC,sBAAS,UAAC,MAAM;AAAA,MACjB,UAAU;AACTD,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,cAAc;AACnB,YAAM,KAAK;AACX,YAAM,KAAK;AACXE,oBAAW,YAAC,MAAM;AAAA,IAClB;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe,GAAG;AACjB,WAAK,cAAc,EAAE,OAAO;AAC5B,WAAK,WAAW,OAAO;AACvB,WAAK,kBAAiB;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa,GAAG;AACf,WAAK,YAAY,EAAE,OAAO;AAC1B,WAAK,WAAW,OAAO;AACvB,WAAK,kBAAiB;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW;AACV,UAAI,KAAK,WAAW,OAAO,GAAG;AAC7B,aAAK,WAAW;AAChB,aAAK,kBAAiB;AAAA,MACvB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW;AACV,UAAI,KAAK,WAAW,OAAO,KAAK,WAAW,YAAY;AACtD,aAAK,WAAW;AAChB,aAAK,kBAAiB;AAAA,MACvB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,kBAAkB,IAAI;AAC3B,UAAI;AACHF,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,cAAM,WAAW,MAAMD,4BAAW,KAAK,wBAAwB,EAAE,EAAE;AAEnE,YAAI,SAAS,SAAS;AACrBG,wBAAW,YAAC,MAAM;AAClB,eAAK,kBAAiB;AAAA,eAChB;AACND,wBAAAA,UAAU,SAAS,WAAW,MAAM;AAAA,QACrC;AAAA,MACC,SAAO,OAAO;AACfD,qGAAc,WAAW,KAAK;AAC9BC,sBAAS,UAAC,MAAM;AAAA,MACjB,UAAU;AACTD,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,mBAAmB,IAAI;AAC5B,UAAI;AACH,cAAM,YAAY,MAAM,IAAI,QAAQ,CAAC,YAAY;AAChDA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS;AAAA,YACT,SAAS,CAAC,QAAQ,QAAQ,IAAI,OAAO;AAAA,UACtC,CAAC;AAAA,QACF,CAAC;AAED,YAAI,CAAC;AAAW;AAEhBA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,cAAM,WAAW,MAAMD,4BAAW,OAAO,kBAAkB,EAAE,EAAE;AAE/D,YAAI,SAAS,SAAS;AACrBG,wBAAW,YAAC,MAAM;AAClB,eAAK,kBAAiB;AAAA,eAChB;AACND,wBAAAA,UAAU,SAAS,WAAW,MAAM;AAAA,QACrC;AAAA,MACC,SAAO,OAAO;AACfD,qGAAc,WAAW,KAAK;AAC9BC,sBAAS,UAAC,MAAM;AAAA,MACjB,UAAU;AACTD,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,uBAAuB;AAC5B,UAAI;AACH,cAAM,YAAY,MAAM,IAAI,QAAQ,CAAC,YAAY;AAChDA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS;AAAA,YACT,SAAS,CAAC,QAAQ,QAAQ,IAAI,OAAO;AAAA,UACtC,CAAC;AAAA,QACF,CAAC;AAED,YAAI,CAAC;AAAW;AAEhBA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,cAAM,WAAW,MAAMD,iBAAAA,WAAW,KAAK,0BAA0B,EAAE,MAAM,EAAA,CAAG;AAE5E,YAAI,SAAS,SAAS;AACrBG,wBAAW,YAAC,YAAY,SAAS,KAAK,YAAY,MAAM;AACxD,eAAK,YAAW;AAAA,eACV;AACND,wBAAAA,UAAU,SAAS,WAAW,MAAM;AAAA,QACrC;AAAA,MACC,SAAO,OAAO;AACfD,qGAAc,WAAW,KAAK;AAC9BC,sBAAS,UAAC,MAAM;AAAA,MACjB,UAAU;AACTD,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,0BAA0B;AAC/B,UAAI,CAAC,KAAK,SAAS,YAAY,CAAC,KAAK,SAAS,UAAU;AACvDC,sBAAS,UAAC,YAAY;AACtB;AAAA,MACD;AAEA,UAAI;AACHD,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,cAAM,WAAW,MAAMD,4BAAW,KAAK,6CAA6C,KAAK,QAAQ;AAEjG,YAAI,SAAS,SAAS;AACrBG,wBAAW,YAAC,SAAS;AACrB,eAAK,gBAAgB;AACrB,eAAK,WAAW,EAAE,UAAU,IAAI,UAAU,IAAI,UAAU;AACxD,qBAAW,MAAM;AAChB,iBAAK,YAAW;AAAA,UAChB,GAAE,GAAI;AAAA,eACD;AACND,wBAAAA,UAAU,SAAS,WAAW,MAAM;AAAA,QACrC;AAAA,MACC,SAAO,OAAO;AACfD,sBAAA,MAAA,MAAA,SAAA,0DAAc,aAAa,KAAK;AAChCC,sBAAS,UAAC,MAAM;AAAA,MACjB,UAAU;AACTD,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe,QAAQ;AACtB,YAAM,WAAW;AAAA,QAChB,WAAW;AAAA,QACX,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,UAAU;AAAA;AAEX,aAAO,SAAS,MAAM,KAAK;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA,IAKD,cAAc,QAAQ;AACrB,YAAM,UAAU;AAAA,QACf,WAAW;AAAA,QACX,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,UAAU;AAAA;AAEX,aAAO,QAAQ,MAAM,KAAK;AAAA,IAC1B;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,MAAM;AACjB,YAAM,UAAU;AAAA,QACf,mBAAmB;AAAA,QACnB,kBAAkB;AAAA;AAEnB,aAAO,QAAQ,IAAI,KAAK;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW,SAAS;AACnB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,aAAO,KAAK,eAAe,OAAO;AAAA,IACnC;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzaD,GAAG,WAAW,eAAe;"}