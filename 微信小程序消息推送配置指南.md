# 微信小程序消息推送配置指南

## 1. 概述

微信小程序消息推送包括两种类型：
- **订阅消息**：用户主动订阅，用于业务通知
- **客服消息**：48小时内可主动发送，用于客服场景

本文档主要介绍订阅消息的配置流程。

## 2. 前期准备

### 2.1 获取小程序基本信息
- **AppID**: 在微信小程序后台获取
- **AppSecret**: 在微信小程序后台获取
- **服务器域名**: `https://www.mls2005.top`
- **服务器IP**: `************`

### 2.2 服务器配置信息
```
HTTP服务器地址: http://************:3000
HTTPS服务器地址: https://www.mls2005.top
Nginx代理端口: 443 (HTTPS) / 80 (HTTP重定向)
内部服务端口: 3000
```

## 3. 微信小程序后台配置

### 3.1 登录微信小程序后台
1. 访问：https://mp.weixin.qq.com/
2. 使用小程序管理员账号登录

### 3.2 配置服务器域名
**路径**: 开发 → 开发管理 → 开发设置 → 服务器域名

配置以下域名：
```
request合法域名: https://www.mls2005.top
uploadFile合法域名: https://www.mls2005.top
downloadFile合法域名: https://www.mls2005.top
```

### 3.3 配置消息推送服务器
**路径**: 开发 → 开发管理 → 开发设置 → 消息推送

#### 服务器配置参数：
```
服务器地址(URL): https://www.mls2005.top/api/wechat/message
Token: your_custom_token_here
EncodingAESKey: 随机生成或自定义43位字符
消息加解密方式: 明文模式（推荐）或兼容模式
数据格式: JSON
```

#### 具体配置步骤：
1. **URL配置**
   - 填入：`https://www.mls2005.top/api/wechat/message`
   - 确保以https://开头，支持443端口

2. **Token配置**
   - 自定义Token（建议使用复杂字符串）
   - 示例：`WechatMsgPush2024_MLS`

3. **EncodingAESKey配置**
   - 点击"随机生成"或手动输入43位字符
   - 示例：`abcdefghijklmnopqrstuvwxyz1234567890ABCDEFG`

4. **启用配置**
   - 点击"启用"按钮
   - 微信会向您的服务器发送验证请求

## 4. 订阅消息模板配置

### 4.1 申请消息模板
**路径**: 功能 → 订阅消息

#### 常用模板类型：
1. **图片识别完成通知**
   ```
   模板标题: 图片识别完成通知
   模板内容:
   {{thing1.DATA}} 
   识别结果：{{thing2.DATA}}
   完成时间：{{time3.DATA}}
   备注：{{thing4.DATA}}
   ```

2. **订单状态更新通知**
   ```
   模板标题: 订单状态更新
   模板内容:
   订单号：{{character_string1.DATA}}
   状态：{{phrase2.DATA}}
   更新时间：{{time3.DATA}}
   说明：{{thing4.DATA}}
   ```

3. **系统异常通知**
   ```
   模板标题: 系统异常通知
   模板内容:
   异常类型：{{thing1.DATA}}
   异常时间：{{time2.DATA}}
   处理建议：{{thing3.DATA}}
   ```

### 4.2 获取模板ID
- 申请通过后，在订阅消息列表中查看模板ID
- 模板ID格式类似：`-WA6pIqaOdGp8YJWlbFBc4Sw_5oP7s1Vy_H-VBhZhZs`

## 5. 服务器端接口实现

### 5.1 消息推送验证接口
**接口地址**: `GET https://www.mls2005.top/api/wechat/message`

```javascript
// 验证微信服务器请求
router.get('/message', (req, res) => {
    const { signature, timestamp, nonce, echostr } = req.query;
    const token = 'WechatMsgPush2024_MLS'; // 与后台配置的Token一致
    
    // 验证签名
    const crypto = require('crypto');
    const tmpStr = [token, timestamp, nonce].sort().join('');
    const tmpSignature = crypto.createHash('sha1').update(tmpStr).digest('hex');
    
    if (tmpSignature === signature) {
        res.send(echostr); // 验证成功，返回echostr
    } else {
        res.status(403).send('验证失败');
    }
});
```

### 5.2 消息推送接收接口
**接口地址**: `POST https://www.mls2005.top/api/wechat/message`

```javascript
// 接收微信推送的消息
router.post('/message', (req, res) => {
    const { ToUserName, FromUserName, CreateTime, MsgType } = req.body;
    
    // 处理不同类型的消息
    switch (MsgType) {
        case 'event':
            // 处理事件消息（如订阅、取消订阅等）
            handleEventMessage(req.body);
            break;
        case 'text':
            // 处理文本消息
            handleTextMessage(req.body);
            break;
        default:
            console.log('未知消息类型:', MsgType);
    }
    
    res.send('success');
});
```

## 6. 网络配置要求

### 6.1 端口配置
```
HTTP端口: 80 (nginx重定向到HTTPS)
HTTPS端口: 443 (nginx代理到内部3000端口)
内部服务端口: 3000 (Express.js应用)
```

### 6.2 防火墙配置
确保以下端口对外开放：
- 80端口：HTTP访问（重定向用）
- 443端口：HTTPS访问（主要服务）
- 3000端口：内部服务（仅本地访问）

### 6.3 SSL证书配置
```
证书文件: /path/to/ssl/www.mls2005.top.pem
私钥文件: /path/to/ssl/www.mls2005.top.key
证书类型: TLS 1.2+
```

## 7. 测试验证

### 7.1 服务器连通性测试
```bash
# 测试HTTP连接
curl -I http://************:3000/api/health

# 测试HTTPS连接
curl -I https://www.mls2005.top/api/health
```

### 7.2 微信接口测试
```bash
# 测试消息推送接口
curl -X GET "https://www.mls2005.top/api/wechat/message?signature=xxx&timestamp=xxx&nonce=xxx&echostr=test"
```

### 7.3 订阅消息发送测试
```javascript
// 发送测试订阅消息
const testData = {
    touser: 'user_openid',
    template_id: 'template_id',
    page: 'pages/index/index',
    data: {
        thing1: { value: '测试通知' },
        time3: { value: '2024-01-01 12:00:00' },
        thing4: { value: '这是一条测试消息' }
    }
};

await sendSubscribeMessage(testData);
```

## 8. 常见问题解决

### 8.1 URL验证失败
**问题**: 微信后台提示"请求URL超时"
**解决方案**:
1. 检查服务器是否正常运行
2. 确认URL格式正确（必须https://开头）
3. 检查防火墙和端口配置
4. 验证SSL证书是否有效

### 8.2 消息发送失败
**问题**: 订阅消息发送返回错误码
**常见错误码**:
- 40037: 模板ID无效
- 41028: form_id不正确或过期
- 41029: form_id已被使用
- 43101: 用户拒绝接受消息

### 8.3 Token验证失败
**问题**: 签名验证不通过
**解决方案**:
1. 确认Token配置一致
2. 检查签名算法实现
3. 验证时间戳和随机数处理

## 9. 安全建议

### 9.1 Token安全
- 使用复杂的Token字符串
- 定期更换Token
- 不要在客户端暴露Token

### 9.2 接口安全
- 验证请求来源
- 实现请求频率限制
- 记录操作日志

### 9.3 数据安全
- 加密敏感数据传输
- 实现用户隐私保护
- 遵循微信平台规范

## 10. 监控和维护

### 10.1 日志监控
- 记录消息发送成功率
- 监控接口响应时间
- 跟踪错误日志

### 10.2 性能优化
- 实现消息队列
- 批量处理机制
- 缓存access_token

### 10.3 定期维护
- 更新SSL证书
- 检查服务器状态
- 优化数据库性能

---

## 附录：完整URL配置清单

### 微信小程序后台配置URL
```
request合法域名: https://www.mls2005.top
uploadFile合法域名: https://www.mls2005.top
downloadFile合法域名: https://www.mls2005.top
消息推送URL: https://www.mls2005.top/api/wechat/message
```

### 服务器访问URL
```
HTTPS主域名: https://www.mls2005.top
HTTPS IP访问: https://************
HTTP重定向: http://www.mls2005.top (自动跳转HTTPS)
内部服务: http://localhost:3000
```

### API接口URL示例
```
健康检查: https://www.mls2005.top/api/health
用户登录: https://www.mls2005.top/api/auth/login
消息推送: https://www.mls2005.top/api/wechat/message
订阅管理: https://www.mls2005.top/api/subscription/save
```
