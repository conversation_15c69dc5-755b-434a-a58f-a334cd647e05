/**
 * 订阅消息功能测试脚本
 * 用于测试微信订阅消息相关功能
 */

const WechatService = require('../utils/wechatService');
const SubscriptionService = require('../utils/subscriptionService');

async function testWechatService() {
    console.log('🧪 开始测试微信服务...');
    
    const wechatService = new WechatService();
    
    try {
        // 测试获取access_token
        console.log('1. 测试获取access_token...');
        const accessToken = await wechatService.getAccessToken();
        console.log('✅ access_token获取成功:', accessToken.substring(0, 20) + '...');
        
        // 测试获取openid（需要有效的code，这里只是示例）
        console.log('2. 测试获取openid（跳过，需要真实的微信code）');
        
        console.log('✅ 微信服务测试完成');
        
    } catch (error) {
        console.error('❌ 微信服务测试失败:', error.message);
    }
}

async function testSubscriptionService() {
    console.log('\n🧪 开始测试订阅服务...');
    
    const subscriptionService = new SubscriptionService();
    
    try {
        // 测试获取订单信息
        console.log('1. 测试获取订单信息...');
        const orderInfo = await subscriptionService.getOrderInfo('TEST_ORDER_001', '测试工厂');
        console.log('订单信息:', orderInfo);
        
        // 测试检查订单通知状态
        console.log('2. 测试检查订单通知状态...');
        const hasSent = await subscriptionService.hasOrderNotificationSent(
            'TEST_ORDER_001', 
            'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8'
        );
        console.log('订单通知状态:', hasSent ? '已发送' : '未发送');
        
        // 测试获取用户订阅信息
        console.log('3. 测试获取用户订阅信息...');
        const userSubscription = await subscriptionService.getUserSubscription(
            'test_user', 
            'pe6RVTNTaFPvBv3BGU-mYi8VV9agbvsc27aGpQtR9c8'
        );
        console.log('用户订阅信息:', userSubscription);
        
        console.log('✅ 订阅服务测试完成');
        
    } catch (error) {
        console.error('❌ 订阅服务测试失败:', error.message);
    }
}

async function testDatabaseConnection() {
    console.log('\n🧪 开始测试数据库连接...');
    
    try {
        const { query } = require('../config/database');
        
        // 测试基本查询
        console.log('1. 测试基本数据库连接...');
        const result = await query('SELECT 1 as test');
        console.log('✅ 数据库连接正常:', result);
        
        // 检查订阅相关表是否存在
        console.log('2. 检查订阅相关表...');
        
        const tables = await query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'identify' 
            AND TABLE_NAME IN ('user_subscriptions', 'message_logs')
        `);
        
        console.log('存在的表:', tables.map(t => t.TABLE_NAME));
        
        if (tables.length === 2) {
            console.log('✅ 订阅相关表已存在');
        } else {
            console.log('⚠️ 订阅相关表不完整，请执行初始化脚本');
        }
        
        // 检查shipping_detail表结构
        console.log('3. 检查shipping_detail表结构...');
        const columns = await query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'identify' 
            AND TABLE_NAME = 'shipping_detail'
            AND COLUMN_NAME IN ('order_no', 'receiver', 'follower', 'created_at')
        `);
        
        console.log('shipping_detail表相关字段:', columns.map(c => c.COLUMN_NAME));
        
        console.log('✅ 数据库测试完成');
        
    } catch (error) {
        console.error('❌ 数据库测试失败:', error.message);
    }
}

async function runAllTests() {
    console.log('🚀 开始运行订阅消息功能测试\n');
    
    await testDatabaseConnection();
    await testWechatService();
    await testSubscriptionService();
    
    console.log('\n🎉 所有测试完成！');
    console.log('\n📝 测试说明:');
    console.log('- 如果access_token获取成功，说明微信配置正确');
    console.log('- 如果数据库连接正常，说明基础环境OK');
    console.log('- 实际的消息发送需要真实的用户openid和订阅状态');
    console.log('- 建议使用微信开发者工具进行完整测试');
    
    process.exit(0);
}

// 运行测试
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('❌ 测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = {
    testWechatService,
    testSubscriptionService,
    testDatabaseConnection
};
